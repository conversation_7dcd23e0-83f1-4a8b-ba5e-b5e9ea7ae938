#!/usr/bin/env python3
"""
测试仪表盘数据流和格式修复
验证所有面板的实时数据更新
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
import random

async def test_redis_data_flow():
    """测试Redis数据流"""
    print("🧪 开始测试仪表盘数据流...")
    
    # 连接Redis
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        await redis_client.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return
    
    # 测试数据
    test_pairs = ["BTC-USDT", "ETH-USDT"]
    
    print("\n📊 发送测试市场数据...")
    for i in range(5):
        for pair in test_pairs:
            # 模拟市场状态数据
            market_data = {
                "event_type": "market_state",
                "payload": {
                    "pair_name": pair,
                    "market_state": {
                        "basis": random.uniform(0.001, 0.005),
                        "spot_price": 45000 + random.uniform(-1000, 1000),
                        "futures_price": 45100 + random.uniform(-1000, 1000),
                        "funding_rate": random.uniform(-0.0001, 0.0001),
                        "bb_middle": random.uniform(0.001, 0.003),
                        "bb_upper": random.uniform(0.003, 0.005),
                        "bb_lower": random.uniform(-0.001, 0.001),
                        "is_realtime": True,
                        "update_source": "test_script"
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await redis_client.publish('trading_bot:market', json.dumps(market_data))
            print(f"📈 发送 {pair} 市场数据 (第{i+1}次)")
        
        await asyncio.sleep(2)
    
    print("\n🔬 发送测试微观结构数据...")
    for pair in test_pairs:
        micro_data = {
            "event_type": "microstructure_update",
            "payload": {
                "pair_name": pair,
                "micro_data": {
                    "obi": random.uniform(-0.5, 0.5),
                    "buy_sell_ratio": random.uniform(0.8, 1.2),
                    "micro_signal": random.choice(["bullish", "bearish", "neutral"]),
                    "micro_confidence": random.uniform(0.5, 0.95),
                    "obi_signal": random.choice(["buy", "sell", "neutral"]),
                    "flow_signal": random.choice(["inflow", "outflow", "neutral"])
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await redis_client.publish('trading_bot:microstructure', json.dumps(micro_data))
        print(f"🔬 发送 {pair} 微观结构数据")
    
    print("\n⚠️ 发送测试警报...")
    alert_levels = ["INFO", "WARNING", "ERROR"]
    alert_messages = [
        "系统运行正常",
        "市场波动较大，请注意风险",
        "连接器出现异常，正在重连"
    ]
    
    for level, message in zip(alert_levels, alert_messages):
        alert_data = {
            "event_type": "alert",
            "payload": {
                "level": level,
                "message": message,
                "details": {
                    "component": "test_script",
                    "test_id": f"test_{level.lower()}"
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await redis_client.publish('trading_bot:alerts', json.dumps(alert_data))
        print(f"⚠️ 发送 {level} 级别警报: {message}")
        await asyncio.sleep(1)
    
    print("\n📊 发送测试系统状态...")
    system_data = {
        "event_type": "system_status",
        "payload": {
            "status": "Running",
            "details": {
                "start_time": datetime.now().isoformat(),
                "runtime_hours": 2.5,
                "trade_pair": "BTC-USDT",
                "subscribed_instruments": 4,
                "position_active": True,
                "connector_healthy": True,
                "data_fresh": True,
                "market_health_score": random.uniform(75, 95),
                "emergency_stop": False
            },
            "timestamp": datetime.now().isoformat()
        }
    }
    
    await redis_client.publish('trading_bot:system', json.dumps(system_data))
    print("📊 发送系统状态数据")
    
    print("\n💼 发送测试持仓数据...")
    position_data = {
        "event_type": "position_opened",
        "payload": {
            "position_id": "test_pos_001",
            "pair_name": "BTC-USDT",
            "position_type": "long_arbitrage",
            "size": 0.1,
            "spot_entry_price": 45000.12345,
            "futures_entry_price": 45100.67890,
            "entry_basis": 0.00223456,
            "open_time": datetime.now().isoformat(),
            "status": "active",
            "unrealized_pnl": 12.5,
            "unrealized_pnl_bps": 2.5
        }
    }
    
    await redis_client.publish('trading_bot:positions', json.dumps(position_data))
    print("💼 发送持仓数据")
    
    print("\n🎯 发送测试交易信号...")
    signal_data = {
        "event_type": "signal_generated",
        "payload": {
            "pair_name": "BTC-USDT",
            "signal_data": {
                "signal_type": "entry_signal",
                "confidence": 0.85,
                "risk_grade": "Medium",
                "expected_profit": 15.5
            },
            "timestamp": datetime.now().isoformat()
        }
    }
    
    await redis_client.publish('trading_bot:market', json.dumps(signal_data))
    print("🎯 发送交易信号数据")
    
    await redis_client.close()
    print("\n✅ 测试数据发送完成！")
    print("📱 请检查仪表盘是否显示了以下内容：")
    print("   1. 实时市场状态面板 - 现货/期货价格（5位小数）")
    print("   2. 实时市场状态面板 - 基差率和资金费率（5位小数）")
    print("   3. 持仓面板 - 详细持仓信息")
    print("   4. 微观结构面板 - OBI和信号数据")
    print("   5. 系统警报面板 - 不同级别的警报")
    print("   6. 系统状态面板 - 运行状态信息")
    print("   7. 关键交易事件面板 - 交易信号")

async def test_format_precision():
    """测试数字格式精度"""
    print("\n🔢 测试数字格式精度...")
    
    test_values = [
        45123.12345,
        0.00123456,
        -0.00098765,
        1234567.89012
    ]
    
    print("测试5位小数格式:")
    for value in test_values:
        formatted = f"{value:.5f}"
        print(f"  {value} -> {formatted}")
    
    print("\n测试百分比格式:")
    for value in [0.00123, -0.00098, 0.12345]:
        pct = value * 100
        formatted = f"{pct:.5f}%"
        print(f"  {value} -> {formatted}")

if __name__ == "__main__":
    print("🧪 仪表盘数据流测试工具")
    print("=" * 50)
    
    asyncio.run(test_format_precision())
    asyncio.run(test_redis_data_flow())
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！现在启动仪表盘查看效果：")
    print("   python trading_dashboard.py")
