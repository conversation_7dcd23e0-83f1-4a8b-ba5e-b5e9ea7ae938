#!/usr/bin/env python3
"""
测试实时数据修复效果
验证基差、资金费率、布林带数据的实时更新
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
import random
import time

async def test_realtime_data_updates():
    """测试实时数据更新修复"""
    print("🧪 测试实时数据更新修复...")
    print("=" * 60)
    
    # 连接Redis
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        await redis_client.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return
    
    # 配置的交易对
    trading_pairs = [
        {"spot_id": "BTC-USDT", "futures_id": "BTC-USDT-SWAP"},
        {"spot_id": "ETH-USDT", "futures_id": "ETH-USDT-SWAP"},
        {"spot_id": "DOGE-USDT", "futures_id": "DOGE-USDT-SWAP"}
    ]
    
    print(f"\n📊 模拟交易机器人发送完整的实时数据...")
    
    # 持续发送数据，模拟修复后的交易机器人数据流
    for round_num in range(15):  # 发送15轮数据
        print(f"\n🔄 第 {round_num + 1} 轮数据更新...")
        
        for pair_config in trading_pairs:
            pair_key = f"{pair_config['spot_id']}-{pair_config['futures_id']}"
            
            # 生成模拟价格数据（每次都变化）
            base_price = 45000 if "BTC" in pair_key else (3000 if "ETH" in pair_key else 0.08)
            spot_price = base_price + random.uniform(-base_price*0.02, base_price*0.02)
            futures_price = spot_price + random.uniform(-base_price*0.001, base_price*0.002)
            
            # 计算基差（每次都重新计算）
            basis = (futures_price - spot_price) / spot_price
            
            # 生成资金费率（模拟WebSocket实时数据）
            funding_rate = random.uniform(-0.0001, 0.0001)
            
            # 生成布林带数据（模拟策略计算结果）
            bb_middle = basis * random.uniform(0.8, 1.2)
            bb_upper = bb_middle + abs(bb_middle) * random.uniform(0.3, 0.7)
            bb_lower = bb_middle - abs(bb_middle) * random.uniform(0.2, 0.5)
            
            # 分层布林带数据
            strategic_bb_middle = bb_middle * random.uniform(0.9, 1.1)
            strategic_bb_upper = strategic_bb_middle + abs(strategic_bb_middle) * 0.5
            strategic_bb_lower = strategic_bb_middle - abs(strategic_bb_middle) * 0.3
            
            tactical_bb_middle = bb_middle * random.uniform(0.95, 1.05)
            tactical_bb_upper = tactical_bb_middle + abs(tactical_bb_middle) * 0.3
            tactical_bb_lower = tactical_bb_middle - abs(tactical_bb_middle) * 0.2
            
            # 发送市场状态数据（模拟修复后的数据结构）
            market_data = {
                "event_type": "market_state",
                "payload": {
                    "pair_name": pair_key,
                    "market_state": {
                        "basis": basis,
                        "spot_price": spot_price,
                        "futures_price": futures_price,
                        "funding_rate": funding_rate,
                        "bb_middle": strategic_bb_middle,  # 使用战略层作为主要显示
                        "bb_upper": strategic_bb_upper,
                        "bb_lower": strategic_bb_lower,
                        "strategic_bb_middle": strategic_bb_middle,
                        "strategic_bb_upper": strategic_bb_upper,
                        "strategic_bb_lower": strategic_bb_lower,
                        "tactical_bb_middle": tactical_bb_middle,
                        "tactical_bb_upper": tactical_bb_upper,
                        "tactical_bb_lower": tactical_bb_lower,
                        "update_source": "websocket_realtime_fixed",
                        "is_realtime": True,
                        "strategy_state": "active",
                        "data_points": round_num + 20  # 模拟足够的历史数据
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await redis_client.publish('trading_bot:market', json.dumps(market_data))
            
            print(f"📈 {pair_key}:")
            print(f"   现货: ${spot_price:.5f}")
            print(f"   期货: ${futures_price:.5f}")
            print(f"   基差: {basis*100:.5f}%")
            print(f"   资金费率: {funding_rate*100:.5f}%")
            print(f"   战略中轨: {strategic_bb_middle*100:.5f}%")
            print(f"   战略上轨: {strategic_bb_upper*100:.5f}%")
            
            # 模拟资金费率WebSocket更新
            funding_data = {
                "event_type": "funding_rate_update",
                "payload": {
                    "inst_id": pair_config['futures_id'],
                    "funding_rate": funding_rate,
                    "funding_time": datetime.now().isoformat(),
                    "update_source": "websocket_realtime"
                },
                "timestamp": datetime.now().isoformat()
            }
            
            await redis_client.publish('trading_bot:funding', json.dumps(funding_data))
        
        await asyncio.sleep(1.5)  # 每1.5秒更新一次，模拟高频更新
    
    await redis_client.close()
    print("\n✅ 实时数据更新测试完成！")

async def monitor_redis_data():
    """监控Redis数据流"""
    print("\n🔍 监控Redis数据流...")
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        pubsub = redis_client.pubsub()
        
        # 订阅市场数据频道
        await pubsub.subscribe('trading_bot:market')
        
        print("📡 开始监控Redis市场数据频道...")
        
        message_count = 0
        async for message in pubsub.listen():
            if message['type'] == 'message':
                message_count += 1
                try:
                    data = json.loads(message['data'])
                    if data.get('event_type') == 'market_state':
                        payload = data.get('payload', {})
                        market_state = payload.get('market_state', {})
                        pair_name = payload.get('pair_name', 'Unknown')
                        
                        print(f"\n📊 收到市场数据 #{message_count} - {pair_name}:")
                        print(f"   基差: {market_state.get('basis', 0)*100:.5f}%")
                        print(f"   资金费率: {market_state.get('funding_rate', 0)*100:.5f}%")
                        print(f"   战略中轨: {market_state.get('strategic_bb_middle', 0)*100:.5f}%")
                        print(f"   数据源: {market_state.get('update_source', 'unknown')}")
                        
                        if message_count >= 10:  # 监控10条消息后退出
                            break
                            
                except Exception as e:
                    print(f"解析消息失败: {e}")
        
        await pubsub.unsubscribe()
        await redis_client.close()
        
    except Exception as e:
        print(f"监控Redis数据失败: {e}")

async def verify_data_consistency():
    """验证数据一致性"""
    print("\n🔍 验证数据一致性...")
    
    # 检查关键修复点
    fixes_verified = []
    
    # 1. 检查arbitrage_bot.py中的修复
    try:
        with open('arbitrage_bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'strategy.get_current_state()' in content:
            fixes_verified.append("✅ 使用get_current_state()获取策略状态")
        else:
            fixes_verified.append("❌ 未使用get_current_state()方法")
        
        if 'self.funding_rates[futures_symbol]' in content:
            fixes_verified.append("✅ 使用WebSocket资金费率数据")
        else:
            fixes_verified.append("❌ 未使用WebSocket资金费率")
        
        if 'layered_bollinger_bands' in content:
            fixes_verified.append("✅ 使用分层布林带数据")
        else:
            fixes_verified.append("❌ 未使用分层布林带")
            
    except Exception as e:
        fixes_verified.append(f"❌ 检查代码失败: {e}")
    
    print("\n📋 修复验证结果:")
    for fix in fixes_verified:
        print(f"   {fix}")
    
    return len([f for f in fixes_verified if f.startswith("✅")]) == 3

if __name__ == "__main__":
    print("🧪 实时数据修复测试")
    print("=" * 60)
    
    # 验证修复
    asyncio.run(verify_data_consistency())
    
    print("\n" + "=" * 60)
    print("🚀 开始实时数据测试...")
    
    # 运行测试
    asyncio.run(test_realtime_data_updates())
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！现在启动仪表盘验证修复效果：")
    print("   python trading_dashboard.py")
    print("\n💡 修复要点：")
    print("   • ✅ 使用strategy.get_current_state()获取最新计算结果")
    print("   • ✅ 优先使用WebSocket资金费率数据")
    print("   • ✅ 正确获取分层布林带数据")
    print("   • ✅ 添加详细调试日志")
    print("   • ✅ 基差、资金费率、布林带应该实时更新")
    
    print("\n⚠️ 如果问题仍然存在，请检查：")
    print("   1. 交易机器人是否正在运行")
    print("   2. WebSocket连接是否正常")
    print("   3. 策略是否有足够的历史数据")
    print("   4. Redis连接是否正常")
