#!/usr/bin/env python3
"""
测试市场数据发布修复
验证交易机器人是否正确发布市场数据到Redis
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
import time

async def monitor_market_data():
    """专门监控市场数据频道"""
    print("🔍 监控市场数据频道...")
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        pubsub = redis_client.pubsub()
        
        # 只订阅市场数据频道
        await pubsub.subscribe('trading_bot:market')
        print("📡 已订阅 trading_bot:market 频道")
        
        print("\n⏰ 开始监控市场数据（60秒）...")
        start_time = time.time()
        message_count = 0
        
        while time.time() - start_time < 60:  # 监控60秒
            try:
                message = await asyncio.wait_for(pubsub.get_message(), timeout=1.0)
                if message and message['type'] == 'message':
                    message_count += 1
                    
                    try:
                        data = json.loads(message['data'])
                        event_type = data.get('event_type', 'unknown')
                        timestamp = data.get('timestamp', 'no_timestamp')
                        
                        print(f"📨 [{message_count:3d}] 市场数据: {event_type} @ {timestamp}")
                        
                        if event_type == 'market_state':
                            payload = data.get('payload', {})
                            pair_name = payload.get('pair_name', 'Unknown')
                            market_state = payload.get('market_state', {})
                            
                            spot_price = market_state.get('spot_price', 0)
                            futures_price = market_state.get('futures_price', 0)
                            basis = market_state.get('basis', 0)
                            funding_rate = market_state.get('funding_rate', 0)
                            bb_middle = market_state.get('bb_middle', 0)
                            bb_upper = market_state.get('bb_upper', 0)
                            update_source = market_state.get('update_source', 'unknown')
                            
                            print(f"     📈 {pair_name}:")
                            print(f"       现货: ${spot_price:.5f}")
                            print(f"       期货: ${futures_price:.5f}")
                            print(f"       基差: {basis*100:.5f}%")
                            print(f"       资金费率: {funding_rate*100:.5f}%")
                            print(f"       中轨: {bb_middle*100:.5f}%")
                            print(f"       上轨: {bb_upper*100:.5f}%")
                            print(f"       数据源: {update_source}")
                        
                    except json.JSONDecodeError:
                        print(f"📨 [{message_count:3d}] 无法解析JSON")
                    except Exception as e:
                        print(f"📨 [{message_count:3d}] 解析错误: {e}")
                        
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                break
        
        await pubsub.unsubscribe()
        await redis_client.aclose()
        
        print(f"\n📊 监控结果:")
        print(f"   市场数据消息数: {message_count}")
        
        if message_count > 0:
            print("✅ 市场数据发布正常")
        else:
            print("❌ 没有收到市场数据")
            print("💡 可能的原因:")
            print("   1. 交易机器人未运行")
            print("   2. WebSocket连接问题")
            print("   3. Redis发布器未初始化")
            print("   4. await关键字缺失（已修复）")
        
        return message_count > 0
        
    except Exception as e:
        print(f"❌ 监控失败: {e}")
        return False

async def check_bot_logs():
    """检查机器人日志中的Redis发布信息"""
    print("\n🔍 检查机器人日志...")
    
    import os
    
    log_files = [
        'arbitrage_bot_structured.log',
        'arbitrage_bot.log'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n📄 检查 {log_file}:")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                # 查找Redis相关的日志
                redis_lines = []
                for line in lines[-50:]:  # 检查最后50行
                    if any(keyword in line.lower() for keyword in ['redis', 'publish', '发布', 'market_data']):
                        redis_lines.append(line.strip())
                
                if redis_lines:
                    print(f"   找到 {len(redis_lines)} 条Redis相关日志:")
                    for i, line in enumerate(redis_lines[-5:], 1):  # 显示最后5条
                        print(f"   [{i}] {line}")
                else:
                    print("   ❌ 没有找到Redis相关日志")
                    
            except Exception as e:
                print(f"   ❌ 读取日志失败: {e}")
        else:
            print(f"❌ {log_file} 不存在")

async def send_test_market_data():
    """发送测试市场数据"""
    print("\n🧪 发送测试市场数据...")
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        
        # 模拟修复后的市场数据结构
        test_data = {
            "event_type": "market_state",
            "payload": {
                "pair_name": "BTC-USDT-BTC-USDT-SWAP",
                "market_state": {
                    "basis": 0.001234,
                    "spot_price": 45000.12345,
                    "futures_price": 45055.67890,
                    "funding_rate": 0.000123,
                    "bb_middle": 0.001100,
                    "bb_upper": 0.001500,
                    "bb_lower": 0.000700,
                    "strategic_bb_middle": 0.001100,
                    "strategic_bb_upper": 0.001500,
                    "strategic_bb_lower": 0.000700,
                    "tactical_bb_middle": 0.001050,
                    "tactical_bb_upper": 0.001350,
                    "tactical_bb_lower": 0.000750,
                    "update_source": "websocket_realtime_fixed",
                    "is_realtime": True,
                    "strategy_state": "active",
                    "data_points": 25
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await redis_client.publish('trading_bot:market', json.dumps(test_data))
        print("📤 已发送测试市场数据")
        
        await redis_client.aclose()
        
    except Exception as e:
        print(f"❌ 发送测试数据失败: {e}")

async def verify_code_fixes():
    """验证代码修复"""
    print("\n🔍 验证代码修复...")
    
    try:
        with open('arbitrage_bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_verified = []
        
        # 检查await关键字修复
        if 'await self._log_market_state_update_realtime(inst_id, last_price, futures_price)' in content:
            fixes_verified.append("✅ 期货价格更新的await修复")
        else:
            fixes_verified.append("❌ 期货价格更新的await未修复")
        
        if 'await self._log_market_state_update_realtime(spot_symbol, spot_price, last_price)' in content:
            fixes_verified.append("✅ 现货价格更新的await修复")
        else:
            fixes_verified.append("❌ 现货价格更新的await未修复")
        
        if 'strategy.get_current_state()' in content:
            fixes_verified.append("✅ 策略状态获取修复")
        else:
            fixes_verified.append("❌ 策略状态获取未修复")
        
        if 'self.funding_rates[futures_symbol]' in content:
            fixes_verified.append("✅ 资金费率获取修复")
        else:
            fixes_verified.append("❌ 资金费率获取未修复")
        
        print("📋 代码修复验证结果:")
        for fix in fixes_verified:
            print(f"   {fix}")
        
        return len([f for f in fixes_verified if f.startswith("✅")]) == 4
        
    except Exception as e:
        print(f"❌ 验证代码修复失败: {e}")
        return False

async def main():
    """主测试流程"""
    print("🧪 市场数据发布修复测试")
    print("=" * 60)
    
    # 1. 验证代码修复
    code_fixed = await verify_code_fixes()
    
    # 2. 检查机器人日志
    await check_bot_logs()
    
    # 3. 发送测试数据
    await send_test_market_data()
    
    # 4. 监控市场数据
    market_data_ok = await monitor_market_data()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   代码修复: {'✅' if code_fixed else '❌'}")
    print(f"   市场数据发布: {'✅' if market_data_ok else '❌'}")
    
    if code_fixed and not market_data_ok:
        print("\n💡 建议:")
        print("   1. 重新启动交易机器人: python arbitrage_bot.py")
        print("   2. 确保WebSocket连接正常")
        print("   3. 检查Redis发布器初始化")
        print("   4. 验证策略有足够的历史数据")
    
    if market_data_ok:
        print("\n🎉 修复成功！现在可以启动仪表盘:")
        print("   python trading_dashboard.py")

if __name__ == "__main__":
    asyncio.run(main())
