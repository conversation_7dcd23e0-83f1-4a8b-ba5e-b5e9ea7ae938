"""
高性能异步期现套利机器人 - 主程序入口
基于WebSocket的毫秒级响应交易系统
"""
import asyncio
import logging
import logging.config
import signal
import sys
from datetime import datetime
from typing import Optional

from config import config, validate_config, validate_security_config, LOGGING_CONFIG, get_enabled_trading_pairs
from arbitrage_bot import ArbitrageBot
# 监控系统已移除 - 使用log_utils.py提供的日志系统


class TradingSystem:
    """交易系统主控制器"""
    
    def __init__(self):
        self.bot: Optional[ArbitrageBot] = None
        self.shutdown_event = asyncio.Event()
        self.logger: Optional[logging.Logger] = None
        
        # 系统状态
        self.start_time: Optional[datetime] = None
        self.is_shutting_down = False
    
    def setup_logging(self):
        """设置日志系统"""
        try:
            # 配置日志
            logging.config.dictConfig(LOGGING_CONFIG)
            self.logger = logging.getLogger(__name__)
            
            # 记录启动信息
            enabled_pairs = get_enabled_trading_pairs()
            self.logger.info("=" * 60)
            self.logger.info("多交易对单边套利机器人启动")
            self.logger.info(f"启动时间: {datetime.now()}")
            self.logger.info(f"配置模式: {'沙盒模式' if config.USE_SANDBOX else '实盘模式'}")
            self.logger.info(f"交易对数量: {len(enabled_pairs)}个")
            for pair in enabled_pairs:
                self.logger.info(f"  - {pair['name']}: {pair['spot_id']} <-> {pair['futures_id']} (优先级:{pair['priority']})")
            self.logger.info("=" * 60)
            
        except Exception as e:
            print(f"日志系统配置失败: {e}")
            sys.exit(1)
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            if not self.is_shutting_down:
                self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
                self.shutdown_event.set()
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)  # 挂起信号（Unix）
    
    def validate_system_requirements(self):
        """验证系统要求"""
        try:
            self.logger.info("验证系统配置...")
            
            # 验证配置文件
            validate_config()
            self.logger.info("✓ 配置文件验证通过")
            
            # 验证Python版本
            if sys.version_info < (3, 8):
                raise RuntimeError("需要Python 3.8或更高版本")
            self.logger.info(f"✓ Python版本: {sys.version}")
            
            # 验证安全配置
            try:
                validate_security_config()
                self.logger.info("✓ 安全配置验证通过")
            except ValueError as e:
                self.logger.error(f"❌ 安全配置验证失败: {e}")
                raise
            
            # 验证网络配置
            if config.PROXY_CONFIG.get("ENABLED"):
                self.logger.info(f"✓ 代理配置: {config.PROXY_CONFIG.get('URL')}")
            
            self.logger.info("系统配置验证完成")
            
        except Exception as e:
            self.logger.error(f"系统配置验证失败: {e}")
            raise
    
    def print_startup_banner(self):
        """打印启动横幅"""
        banner = f"""
    ╔══════════════════════════════════════════════════════════╗
    ║                异步期现套利机器人 v2.0                      ║
    ║                  高性能WebSocket架构                       ║
    ╠══════════════════════════════════════════════════════════╣
    ║ 交易所: OKX                                              ║
    ║ 模式: {'沙盒测试' if config.USE_SANDBOX else '实盘交易'}                                       ║
    ║ 策略: 基差套利 (布林带统计套利)                              ║
    ║ 交易对: {config.SPOT_ID} <-> {config.FUTURES_ID}          ║
    ╠══════════════════════════════════════════════════════════╣
    ║ 风控参数:                                                ║
    ║ - 最大持仓时间: {config.PAIR_CONFIG.get('max_holding_duration_hours', 24)}小时                            ║
    ║ - 止损阈值: {config.RISK_MANAGEMENT.get('STOP_LOSS_PERCENT', 0.02)*100:.1f}%                              ║
    ║ - 基差入场阈值: {config.BASIS_ENTRY_THRESHOLD*100:.2f}%                         ║
    ║ - 单笔最大资金: {config.RISK_MANAGEMENT.get('MAX_CAPITAL_PER_TRADE_RATIO', 0.25)*100:.0f}%总资金              ║
    ╚══════════════════════════════════════════════════════════╝
        """
        print(banner)
        self.logger.info("系统启动横幅已显示")
    
    async def initialize_bot(self):
        """初始化交易机器人"""
        try:
            self.logger.info("初始化交易机器人...")
            
            # 创建机器人实例
            self.bot = ArbitrageBot(config)
            
            self.logger.info("交易机器人初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化交易机器人失败: {e}")
            raise
    
    # 监控系统已移除 - 性能指标通过log_utils.py的结构化日志记录
    
    async def run_bot(self):
        """运行交易机器人"""
        try:
            self.start_time = datetime.now()
            self.logger.info("启动交易机器人主循环...")
            
            # 启动机器人（这会开始WebSocket消息处理循环）
            bot_task = asyncio.create_task(self.bot.start(), name="arbitrage_bot")
            
            # 等待关闭信号或机器人任务完成
            shutdown_task = asyncio.create_task(self.shutdown_event.wait(), name="shutdown_signal")
            
            try:
                # 等待第一个完成的任务
                done, pending = await asyncio.wait(
                    [bot_task, shutdown_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 取消还在运行的任务
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                
                # 检查是否有异常
                for task in done:
                    if task.exception():
                        raise task.exception()
                
            except Exception as e:
                self.logger.error(f"机器人运行时异常: {e}")
                raise
            
        except Exception as e:
            self.logger.error(f"运行交易机器人失败: {e}")
            raise
    
    async def shutdown_bot(self):
        """关闭交易机器人"""
        if self.is_shutting_down:
            return
        
        self.is_shutting_down = True
        
        try:
            self.logger.info("开始关闭交易机器人...")
            
            if self.bot:
                await self.bot.stop()
            
            # 停止监控服务器
            try:
                # 监控系统已移除
                self.logger.info("监控服务器已停止")
            except Exception as e:
                self.logger.warning(f"停止监控服务器时出现警告: {e}")
            
            # 计算运行时长
            if self.start_time:
                runtime = datetime.now() - self.start_time
                self.logger.info(f"机器人总运行时长: {runtime}")
            
            self.logger.info("交易机器人已安全关闭")
            
        except Exception as e:
            self.logger.error(f"关闭交易机器人时发生错误: {e}")
    
    async def main_loop(self):
        """主事件循环"""
        try:
            # 初始化系统
            self.setup_logging()
            self.setup_signal_handlers()
            self.print_startup_banner()
            self.validate_system_requirements()
            
            # 监控系统已移除 - 使用log_utils.py进行性能监控
            
            # 初始化并运行机器人
            await self.initialize_bot()
            await self.run_bot()
            
        except KeyboardInterrupt:
            self.logger.info("接收到键盘中断信号")
        except Exception as e:
            self.logger.critical(f"系统发生严重错误: {e}")
            raise
        finally:
            await self.shutdown_bot()


async def main():
    """异步主函数"""
    system = TradingSystem()
    
    try:
        await system.main_loop()
    except Exception as e:
        print(f"系统启动失败: {e}")
        sys.exit(1)


def run():
    """程序入口点"""
    try:
        # 检查事件循环策略（Windows兼容性）
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行异步主函数
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常退出: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run()