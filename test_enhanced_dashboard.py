#!/usr/bin/env python3
"""
测试增强版仪表盘功能
"""
import asyncio
import logging
from okx_connector import OKXConnector
from config import config, get_enabled_trading_pairs

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_market_data():
    """测试市场数据获取功能"""
    logger.info("开始测试市场数据获取...")

    # 初始化OKX连接器
    connector = OKXConnector(config)

    try:
        # 测试获取交易对数据
        trading_pairs = get_enabled_trading_pairs()
        for pair_config in trading_pairs[:1]:  # 只测试第一个交易对
            spot_id = pair_config['spot_id']
            futures_id = pair_config['futures_id']

            logger.info(f"测试交易对: {spot_id} / {futures_id}")

            # 获取现货价格
            try:
                spot_ticker = await connector.get_ticker(spot_id)
                if spot_ticker:
                    logger.info(f"现货价格 ({spot_id}): ${spot_ticker['last']}")
                else:
                    logger.warning(f"无法获取现货价格: {spot_id}")
            except Exception as e:
                logger.warning(f"获取现货价格失败: {e}")

            # 获取期货价格
            try:
                futures_ticker = await connector.get_ticker(futures_id)
                if futures_ticker:
                    logger.info(f"期货价格 ({futures_id}): ${futures_ticker['last']}")
                else:
                    logger.warning(f"无法获取期货价格: {futures_id}")
            except Exception as e:
                logger.warning(f"获取期货价格失败: {e}")

            # 获取资金费率
            try:
                funding_data = await connector.get_funding_rate(futures_id)
                if funding_data:
                    funding_rate = float(funding_data['fundingRate']) * 100
                    logger.info(f"资金费率 ({futures_id}): {funding_rate:.4f}%")
                else:
                    logger.warning(f"无法获取资金费率: {futures_id}")
            except Exception as e:
                logger.warning(f"获取资金费率失败: {e}")

            logger.info("-" * 50)

    except Exception as e:
        logger.error(f"测试失败: {e}")

def test_position_functions():
    """测试持仓相关函数"""
    logger.info("测试持仓相关函数...")
    
    # 模拟持仓数据
    mock_position = {
        'status': 'open',
        'open_time': '2024-01-15T10:30:00Z',
        'spot_entry_price': 45000,
        'futures_entry_price': 45100,
        'size': 0.1,
        'entry_basis': 0.002,  # 0.2%
        'unrealized_pnl': 25.5
    }
    
    # 导入测试函数
    import sys
    sys.path.append('.')
    
    try:
        from trading_dashboard import (
            get_position_status, 
            calculate_entry_info,
            calculate_profit_target,
            calculate_position_duration,
            format_unrealized_pnl,
            calculate_risk_level
        )
        
        # 测试持仓状态
        status = get_position_status(mock_position)
        logger.info(f"持仓状态: {status}")
        
        # 测试入场信息
        entry_cost, entry_basis = calculate_entry_info("BTC-USDT-BTC-USDT-SWAP", mock_position)
        logger.info(f"入场成本: {entry_cost}, 入场基差率: {entry_basis}")
        
        # 测试目标止盈
        target_profit, profit_distance = calculate_profit_target("BTC-USDT-BTC-USDT-SWAP", mock_position, 0.0025)
        logger.info(f"目标止盈: {target_profit}, 止盈距离: {profit_distance}")
        
        # 测试持仓时长
        duration = calculate_position_duration(mock_position)
        logger.info(f"持仓时长: {duration}")
        
        # 测试未实现PNL格式化
        pnl_display = format_unrealized_pnl(mock_position, 0.0025)
        logger.info(f"未实现PNL: {pnl_display}")
        
        # 测试风险度
        risk_level = calculate_risk_level(mock_position, 0.0025)
        logger.info(f"风险度: {risk_level}")
        
    except Exception as e:
        logger.error(f"测试持仓函数失败: {e}")

async def main():
    """主测试函数"""
    logger.info("开始增强版仪表盘功能测试")
    
    # 测试市场数据获取
    await test_market_data()
    
    # 测试持仓函数
    test_position_functions()
    
    logger.info("测试完成")

if __name__ == "__main__":
    asyncio.run(main())
