# 异步期现套利机器人依赖 - WebSocket高频交易架构
# 核心异步库
aiohttp==3.12.14                # 异步HTTP客户端
websockets==12.0                # WebSocket客户端库
# asyncio-mqtt==0.14.0           # 异步MQTT客户端（已移除：未使用）

# 数据处理和数值计算（仅strategy.py使用）
numpy==2.3.1                   # 数值计算（布林带、统计分析）
pandas==2.3.1                  # 数据处理和时间序列
pandas-ta==0.3.14b             # 技术分析指标库（ATR、ADX等）

# 加密和安全
cryptography==45.0.5           # 加密库（API签名）

# 网络和连接
aiohttp_socks==0.10.1          # SOCKS代理支持
python-socks==2.7.1            # SOCKS客户端
PySocks==1.7.1                 # SOCKS代理
certifi==2025.7.14             # SSL证书

# 配置和环境
python-dotenv==1.1.1           # 环境变量加载

# 时间和日期处理
python-dateutil==2.9.0.post0   # 日期时间工具
pytz==2025.2                   # 时区处理

# 监控和指标
# prometheus-client==0.21.1       # Prometheus指标暴露（已移除：监控系统已删除）

# 用户界面和仪表板
rich==14.1.0                    # 终端UI库（交易仪表板）

# 消息队列和缓存
redis==5.0.8                    # Redis客户端（消息队列）
redis[hiredis]==5.0.8          # Redis客户端（异步，含hiredis优化）

# 回测框架（暂未使用，已移除）
# vectorbt # 添加 vectobt

# 开发和测试工具
pytest==7.4.3                # 测试框架
pytest-asyncio==0.23.3       # 异步测试支持
pytest-mock==3.12.0          # Mock支持
# black==23.12.1                # 代码格式化
# flake8==6.1.0                 # 代码检查

# 依赖库（自动安装）
aiosignal==1.4.0
attrs==25.3.0
charset-normalizer==3.4.2
frozenlist==1.7.0
idna==3.10
multidict==6.6.3
yarl==1.20.1
typing_extensions==4.14.1

# 注意：
# 1. 使用 Python 3.8+ 运行
# 2. 建议在虚拟环境中安装：python -m venv venv && source venv/bin/activate
# 3. 安装命令：pip install -r requirements.txt
# 4. 如需OKX官方SDK，可添加：okx-python-sdk-api==1.0.0
