#!/usr/bin/env python3
"""
测试增强的微观结构面板和数据流
验证三层架构显示效果
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
import random
import time

async def test_enhanced_microstructure():
    """测试增强的微观结构数据流"""
    print("🧪 开始测试增强的微观结构面板...")
    
    # 连接Redis
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        await redis_client.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return
    
    # 配置的交易对（应该与config.py中的一致）
    trading_pairs = [
        {"spot_id": "BTC-USDT", "futures_id": "BTC-USDT-SWAP"},
        {"spot_id": "ETH-USDT", "futures_id": "ETH-USDT-SWAP"},
        {"spot_id": "DOGE-USDT", "futures_id": "DOGE-USDT-SWAP"}
    ]
    
    print(f"\n📊 为 {len(trading_pairs)} 个交易对发送微观结构数据...")
    
    # 模拟实时微观结构数据更新
    for round_num in range(10):  # 发送10轮数据
        print(f"\n🔄 第 {round_num + 1} 轮数据更新...")
        
        for pair_config in trading_pairs:
            pair_key = f"{pair_config['spot_id']}-{pair_config['futures_id']}"
            
            # 生成模拟的微观结构数据
            obi = random.uniform(-0.8, 0.8)  # 订单簿不平衡
            buy_sell_ratio = random.uniform(0.5, 2.0)  # 买卖比例
            micro_confidence = random.uniform(0.3, 0.95)  # 置信度
            
            # 根据数据生成信号
            if obi > 0.3:
                micro_signal = "bullish"
                obi_signal = "buy"
            elif obi < -0.3:
                micro_signal = "bearish"
                obi_signal = "sell"
            else:
                micro_signal = "neutral"
                obi_signal = "neutral"
            
            if buy_sell_ratio > 1.2:
                flow_signal = "inflow"
            elif buy_sell_ratio < 0.8:
                flow_signal = "outflow"
            else:
                flow_signal = "neutral"
            
            # 构造微观结构数据
            micro_data = {
                "event_type": "microstructure_update",
                "payload": {
                    "pair_name": pair_key,
                    "micro_data": {
                        "obi": obi,
                        "buy_sell_ratio": buy_sell_ratio,
                        "micro_signal": micro_signal,
                        "micro_confidence": micro_confidence,
                        "obi_signal": obi_signal,
                        "flow_signal": flow_signal,
                        # 额外的模拟数据
                        "spread": abs(obi) * 0.01,
                        "trade_intensity": int(micro_confidence * 500),
                        "large_trades": [
                            {"side": "buy" if random.random() > 0.5 else "sell", 
                             "size": round(random.uniform(1.0, 5.0), 1),
                             "price": 45000 + random.uniform(-100, 100)},
                            {"side": "buy" if random.random() > 0.5 else "sell", 
                             "size": round(random.uniform(0.5, 3.0), 1),
                             "price": 45000 + random.uniform(-100, 100)}
                        ]
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            # 发布到Redis
            await redis_client.publish('trading_bot:microstructure', json.dumps(micro_data))
            
            print(f"📈 {pair_key}: OBI={obi:+.3f}, 买卖比={buy_sell_ratio:.2f}, "
                  f"信号={micro_signal}, 置信度={micro_confidence:.2f}")
        
        # 同时发送一些市场数据，确保基差等数据也在更新
        for pair_config in trading_pairs:
            pair_key = f"{pair_config['spot_id']}-{pair_config['futures_id']}"
            
            spot_price = 45000 + random.uniform(-1000, 1000)
            futures_price = spot_price + random.uniform(-50, 100)
            basis = (futures_price - spot_price) / spot_price
            
            market_data = {
                "event_type": "market_state",
                "payload": {
                    "pair_name": pair_key,
                    "market_state": {
                        "basis": basis,
                        "spot_price": spot_price,
                        "futures_price": futures_price,
                        "funding_rate": random.uniform(-0.0001, 0.0001),
                        "bb_middle": basis * 0.8,
                        "bb_upper": basis * 1.2,
                        "bb_lower": basis * 0.6,
                        "is_realtime": True,
                        "update_source": "test_enhanced"
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await redis_client.publish('trading_bot:market', json.dumps(market_data))
        
        await asyncio.sleep(3)  # 每3秒更新一次
    
    print("\n🎯 发送一些测试警报...")
    
    # 发送不同级别的警报
    alerts = [
        {"level": "INFO", "message": "微观结构数据流正常"},
        {"level": "WARNING", "message": "BTC-USDT 订单簿不平衡加剧"},
        {"level": "ERROR", "message": "ETH-USDT 大额卖单检测"},
        {"level": "INFO", "message": "DOGE-USDT 交易强度上升"}
    ]
    
    for alert in alerts:
        alert_data = {
            "event_type": "alert",
            "payload": {
                "level": alert["level"],
                "message": alert["message"],
                "details": {
                    "component": "microstructure_test",
                    "timestamp": datetime.now().isoformat()
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await redis_client.publish('trading_bot:alerts', json.dumps(alert_data))
        print(f"⚠️ 发送 {alert['level']} 警报: {alert['message']}")
        await asyncio.sleep(1)
    
    await redis_client.close()
    print("\n✅ 增强微观结构测试完成！")
    print("\n📱 请检查仪表盘微观结构面板是否显示：")
    print("   1. 🎯 核心压力指标 - 买卖压力条、OBI、成交量压力")
    print("   2. 📊 订单簿状态 - 价差、盘口深度可视化")
    print("   3. 🔄 交易流分析 - 交易强度、信号状态、大单追踪")
    print("   4. 🔢 所有数值都应该是5位小数精度")
    print("   5. 📈 数据应该每3秒实时更新")

async def test_data_precision():
    """测试数据精度显示"""
    print("\n🔢 测试数据精度...")
    
    test_values = [
        ("OBI", 0.12345),
        ("基差", 0.00234567),
        ("资金费率", -0.00012345),
        ("现货价格", 45123.67890),
        ("期货价格", 45234.12345)
    ]
    
    print("验证5位小数格式:")
    for name, value in test_values:
        if "价格" in name:
            formatted = f"${value:.5f}"
        elif "率" in name:
            formatted = f"{value * 100:.5f}%"
        else:
            formatted = f"{value:.5f}"
        print(f"  {name}: {value} -> {formatted}")

if __name__ == "__main__":
    print("🧪 增强微观结构面板测试工具")
    print("=" * 60)
    
    asyncio.run(test_data_precision())
    asyncio.run(test_enhanced_microstructure())
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！现在启动仪表盘查看新的微观结构面板：")
    print("   python trading_dashboard.py")
    print("\n💡 新的微观结构面板特点：")
    print("   • 三层架构：核心压力 → 订单簿状态 → 交易流分析")
    print("   • 显示所有配置的交易对")
    print("   • 实时数据更新（每3秒）")
    print("   • 可视化压力条和盘口深度")
    print("   • 5位小数精度显示")
