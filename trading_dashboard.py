"""
实时交易仪表盘 - 基于Redis消息队列的高性能架构
支持低延迟实时数据更新和丰富的可视化功能
"""
import asyncio
import json
import logging
from collections import deque
from datetime import datetime
from typing import Dict, Any, Optional

import redis.asyncio as redis
from rich.console import Console
from rich.layout import Layout
from rich.live import Live
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.progress import SpinnerColumn, TextColumn, Progress

# 注意：仪表盘不再直接调用OKX API，所有数据都来自Redis消息
# from okx_connector import OKXConnector  # 已移除

# --- 配置项 ---
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_PASSWORD = None
REDIS_DB = 0
MAX_LOG_LINES = 15
MAX_BASIS_HISTORY = 50  # 用于基差历史图的最大历史点数
REFRESH_RATE = 1.0  # UI刷新频率（秒）

# Redis频道配置
REDIS_CHANNELS = {
    'system': 'trading_bot:system',
    'positions': 'trading_bot:positions', 
    'market': 'trading_bot:market',
    'microstructure': 'trading_bot:microstructure',
    'alerts': 'trading_bot:alerts'
}

# --- 全局状态管理 ---
console = Console()
layout = Layout()

# 注意：不再使用OKX连接器，所有数据来自Redis
# okx_connector = None  # 已移除

# 数据存储结构
system_status = {
    "status": "Initializing...",
    "last_update": "N/A",
    "ws_connection": "Disconnected",
    "start_time": None,
    "trade_pair": "Unknown",
    "subscribed_instruments": 0,
    "redis_connection": "Disconnected"
}

active_positions = {}

market_states = {
    # 'BTC-USDT': {
    #     'basis': 0.0,
    #     'basis_history': deque(maxlen=MAX_BASIS_HISTORY),
    #     'spot_price': 0.0,
    #     'futures_price': 0.0,
    #     'bb_middle': 0.0,
    #     'bb_upper': 0.0,
    #     'bb_lower': 0.0,
    #     'last_update': '',
    #     'is_realtime': False
    # }
}

microstructure_states = {
    # 'BTC-USDT': {
    #     'obi': 0.0,
    #     'buy_sell_ratio': 1.0,
    #     'micro_signal': 'neutral',
    #     'micro_confidence': 0.0
    # }
}

api_limiter_status = {
    'order_rate_limiter': {
        'tokens': 0,
        'capacity': 1,
        'refill_rate': 0.1
    }
}

last_trade_events = deque(maxlen=MAX_LOG_LINES)
last_alerts = deque(maxlen=MAX_LOG_LINES)

# 连接状态
connection_stats = {
    'redis_connected': False,
    'messages_received': 0,
    'last_message_time': None,
    'connection_errors': 0
}


class DashboardApp:
    """仪表盘主应用类 - 管理Redis连接和事件分发"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub = None
        self.data_queue = asyncio.Queue()
        self.is_running = False
        self.logger = logging.getLogger(__name__)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    async def connect_redis(self) -> bool:
        """连接到Redis服务器"""
        try:
            self.redis_client = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                password=REDIS_PASSWORD,
                db=REDIS_DB,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            
            # 测试连接
            await self.redis_client.ping()
            connection_stats['redis_connected'] = True
            system_status['redis_connection'] = "Connected"
            
            self.logger.info(f"✅ Redis连接成功: {REDIS_HOST}:{REDIS_PORT}")
            return True
            
        except Exception as e:
            connection_stats['redis_connected'] = False
            connection_stats['connection_errors'] += 1
            system_status['redis_connection'] = f"Failed: {str(e)[:30]}"
            
            self.logger.error(f"❌ Redis连接失败: {e}")
            return False
    
    async def _message_handler(self, message):
        """处理Redis消息并放入内部队列"""
        try:
            if message['type'] == 'message':
                data = json.loads(message['data'])
                await self.data_queue.put(data)
                
                # 更新统计信息
                connection_stats['messages_received'] += 1
                connection_stats['last_message_time'] = datetime.now()
                
        except (json.JSONDecodeError, KeyError) as e:
            self.logger.warning(f"解析Redis消息失败: {e}")
        except Exception as e:
            self.logger.error(f"处理Redis消息失败: {e}")
    
    async def _subscribe_to_redis(self):
        """订阅Redis频道"""
        if not self.redis_client:
            return

        try:
            self.pubsub = self.redis_client.pubsub()

            # 订阅所有频道
            channels_to_subscribe = list(REDIS_CHANNELS.values())
            await self.pubsub.subscribe(*channels_to_subscribe)

            self.logger.info(f"📡 已订阅Redis频道: {list(REDIS_CHANNELS.keys())}")
            self.logger.info(f"📡 订阅的具体频道: {channels_to_subscribe}")

            # 持续监听消息
            while self.is_running:
                try:
                    # 使用超时避免无限阻塞
                    message = await asyncio.wait_for(self.pubsub.get_message(), timeout=1.0)
                    if message and message['type'] == 'message':
                        self.logger.debug(f"收到Redis消息: {message['channel']}")
                        await self._message_handler(message)
                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except Exception as e:
                    self.logger.error(f"处理Redis消息时出错: {e}")
                    await asyncio.sleep(0.1)

        except Exception as e:
            self.logger.error(f"❌ Redis订阅失败: {e}")
            connection_stats['redis_connected'] = False
            system_status['redis_connection'] = f"Subscription failed: {str(e)[:30]}"
    
    async def _dispatch_events(self):
        """从内部队列取出数据并分发给状态更新函数"""
        while self.is_running:
            try:
                # 设置超时避免阻塞
                data = await asyncio.wait_for(self.data_queue.get(), timeout=1.0)
                
                event_type = data.get('event_type')
                payload = data.get('payload', {})
                
                # 根据事件类型分发处理
                if event_type == 'system_status':
                    self.update_system_status(payload)
                elif event_type in ['position_opened', 'position_closed', 'position_recovered']:
                    self.update_positions(event_type, payload)
                elif event_type == 'market_state':
                    self.update_market_state(payload)
                elif event_type == 'signal_generated':
                    self.add_signal_event(payload)
                elif event_type == 'microstructure_update':
                    self.update_microstructure(payload)
                elif event_type == 'api_limiter_status':
                    self.update_api_limiter(payload)
                elif event_type == 'alert':
                    self.add_alert(payload)
                else:
                    # 其他交易事件作为trade events处理
                    self.add_trade_event(event_type, payload)
                
                self.data_queue.task_done()
                
            except asyncio.TimeoutError:
                continue  # 超时继续循环
            except Exception as e:
                self.logger.error(f"事件分发失败: {e}")
    
    def update_system_status(self, payload: Dict[str, Any]):
        """更新系统状态"""
        details = payload.get('details', {})
        system_status.update({
            'status': payload.get('status', 'Unknown'),
            'last_update': payload.get('timestamp', datetime.now().isoformat()),
            'start_time': details.get('start_time'),
            'trade_pair': details.get('trade_pair', system_status.get('trade_pair')),
            'subscribed_instruments': details.get('subscribed_instruments', 0)
        })
        
        # WebSocket连接状态（从Redis消息推断）
        if payload.get('status') == 'Running':
            system_status['ws_connection'] = "Connected"
    
    def update_positions(self, event_type: str, payload: Dict[str, Any]):
        """更新仓位信息"""
        pair_name = payload.get('pair_name', 'Unknown')
        position_data = payload.get('position_data', {})
        
        if event_type in ['position_opened', 'position_recovered']:
            active_positions[pair_name] = {
                'position_id': position_data.get('position_id', 'Unknown'),
                'type': position_data.get('position_type', 'Unknown'),
                'size': position_data.get('final_position_size', 0.0),
                'entry_basis': position_data.get('entry_basis', 0.0),
                'entry_spot_price': position_data.get('entry_spot_price', 0.0),
                'entry_futures_price': position_data.get('entry_futures_price', 0.0),
                'unrealized_pnl': position_data.get('unrealized_pnl', 0.0),
                'open_time': position_data.get('open_time')
            }
        elif event_type == 'position_closed':
            if pair_name in active_positions:
                del active_positions[pair_name]
    
    def update_market_state(self, payload: Dict[str, Any]):
        """更新市场状态"""
        pair_name = payload.get('pair_name', 'Unknown')
        market_data = payload.get('market_state', {})
        
        if pair_name not in market_states:
            market_states[pair_name] = {
                'basis_history': deque(maxlen=MAX_BASIS_HISTORY)
            }
        
        # 更新基差历史（用于sparkline图表）
        basis = market_data.get('basis', 0.0)
        if basis is not None:
            market_states[pair_name]['basis_history'].append(basis * 100)  # 转换为基点
        else:
            market_states[pair_name]['basis_history'].append(0.0)
        
        # 更新其他市场状态
        market_states[pair_name].update({
            'basis': basis,
            'spot_price': market_data.get('spot_price', 0.0),
            'futures_price': market_data.get('futures_price', 0.0),
            'funding_rate': market_data.get('funding_rate', 0.0),
            'bb_middle': market_data.get('bb_middle', 0.0),
            'bb_upper': market_data.get('bb_upper', 0.0),
            'bb_lower': market_data.get('bb_lower', 0.0),
            'last_update': payload.get('timestamp', ''),
            'is_realtime': market_data.get('is_realtime', False),
            'update_source': market_data.get('update_source', 'unknown')
        })
    
    def add_signal_event(self, payload: Dict[str, Any]):
        """添加交易信号事件"""
        pair_name = payload.get('pair_name', 'Unknown')
        signal_data = payload.get('signal_data', {})
        timestamp = payload.get('timestamp', datetime.now().isoformat())
        
        # 格式化信号消息
        signal_type = signal_data.get('signal_type', 'Unknown')
        confidence = signal_data.get('confidence', 0.0)
        risk_grade = signal_data.get('risk_grade', 'Unknown')
        
        message = (f"[{timestamp[-8:]}] [bold green]信号[/]: {pair_name} - {signal_type} | "
                  f"评级: {risk_grade} | 置信度: {confidence:.2f}")
        
        last_trade_events.append(message)
    
    def update_microstructure(self, payload: Dict[str, Any]):
        """更新微观结构数据"""
        pair_name = payload.get('pair_name', 'Unknown')
        micro_data = payload.get('micro_data', {})
        
        microstructure_states[pair_name] = {
            'obi': micro_data.get('obi', 0.0),
            'buy_sell_ratio': micro_data.get('buy_sell_ratio', 1.0),
            'micro_signal': micro_data.get('micro_signal', 'neutral'),
            'micro_confidence': micro_data.get('micro_confidence', 0.0),
            'obi_signal': micro_data.get('obi_signal', 'neutral'),
            'flow_signal': micro_data.get('flow_signal', 'neutral'),
            'last_update': datetime.now().isoformat()
        }
    
    def update_api_limiter(self, payload: Dict[str, Any]):
        """更新API限制器状态"""
        limiter_data = payload.get('limiter_data', {})
        api_limiter_status.update(limiter_data)
    
    def add_alert(self, payload: Dict[str, Any]):
        """添加警报"""
        level = payload.get('level', 'INFO').upper()
        message = payload.get('message', '')
        timestamp = payload.get('timestamp', datetime.now().isoformat())
        
        # 警报图标和颜色映射
        icon_map = {
            "INFO": "ℹ️",
            "WARNING": "⚠️", 
            "ERROR": "❌",
            "CRITICAL": "🔥"
        }
        color_map = {
            "INFO": "white",
            "WARNING": "yellow",
            "ERROR": "red", 
            "CRITICAL": "bold red on white"
        }
        
        icon = icon_map.get(level, "ℹ️")
        color = color_map.get(level, "white")
        
        formatted_message = f"[{timestamp[-8:]}] [{color}]{icon} {message}[/]"
        last_alerts.append(formatted_message)
    
    def add_trade_event(self, event_type: str, payload: Dict[str, Any]):
        """添加交易事件"""
        pair_name = payload.get('pair_name', 'Unknown')
        timestamp = payload.get('timestamp', datetime.now().isoformat())
        
        if event_type == 'position_opened':
            trade_data = payload.get('trade_data', {})
            entry_basis = trade_data.get('entry_basis', 0)
            entry_basis_pct = (entry_basis * 100) if entry_basis is not None else 0.0
            message = (f"[{timestamp[-8:]}] [bold cyan]开仓[/]: {pair_name} | "
                      f"基差: {entry_basis_pct:.4f}%")
        elif event_type == 'position_closed':
            trade_data = payload.get('trade_data', {})
            pnl = trade_data.get('realized_pnl', 0.0)
            pnl_style = "green" if pnl >= 0 else "red"
            message = (f"[{timestamp[-8:]}] [bold magenta]平仓[/]: {pair_name} | "
                      f"盈亏: [{pnl_style}]{pnl:.4f}[/]")
        else:
            message = f"[{timestamp[-8:]}] {event_type}: {pair_name}"
        
        last_trade_events.append(message)
    
    async def start(self):
        """启动仪表盘应用"""
        self.is_running = True
        
        # 连接Redis
        if not await self.connect_redis():
            console.print("[bold red]Redis连接失败，仪表盘将无法接收实时数据[/bold red]")
            return False
        
        # 启动异步任务
        redis_task = asyncio.create_task(self._subscribe_to_redis())
        dispatcher_task = asyncio.create_task(self._dispatch_events())
        
        try:
            # 启动后台任务，不等待完成
            await asyncio.gather(redis_task, dispatcher_task, return_exceptions=True)
        except Exception as e:
            self.logger.error(f"后台任务异常: {e}")
        finally:
            self.is_running = False
            if self.pubsub:
                await self.pubsub.unsubscribe()
            if self.redis_client:
                await self.redis_client.aclose()
    
    def stop(self):
        """停止仪表盘应用"""
        self.is_running = False


# --- UI面板生成函数 ---

def make_header() -> Panel:
    """生成仪表盘头部"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    redis_status = "🟢" if connection_stats['redis_connected'] else "🔴"
    
    header_text = Text.assemble(
        ("高频套利机器人 - 实时监控仪表盘", "bold magenta"),
        ("  |  ", "white"),
        (f"Redis {redis_status}", "white"),
        ("  |  ", "white"), 
        (current_time, "cyan")
    )
    return Panel(header_text, border_style="green")


def make_system_status_panel() -> Panel:
    """生成系统状态面板"""
    status_table = Table.grid(expand=True)
    status_table.add_column(style="cyan")
    status_table.add_column(style="white")
    
    # 机器人状态
    status_color = "green" if "Running" in system_status['status'] else "yellow"
    status_table.add_row("机器人状态:", f"[bold {status_color}]{system_status['status']}[/]")
    
    # 最后心跳
    last_update = system_status['last_update']
    if last_update != "N/A":
        try:
            update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
            display_time = update_time.strftime("%H:%M:%S")
        except:
            display_time = last_update[-8:] if len(last_update) >= 8 else last_update
    else:
        display_time = "N/A"
    
    status_table.add_row("最后心跳:", display_time)
    
    # WebSocket连接
    ws_style = "green" if system_status['ws_connection'] == "Connected" else "red"
    status_table.add_row("WebSocket:", f"[{ws_style}]{system_status['ws_connection']}[/]")
    
    # Redis连接
    redis_style = "green" if connection_stats['redis_connected'] else "red"
    redis_status = "Connected" if connection_stats['redis_connected'] else system_status.get('redis_connection', 'Disconnected')
    status_table.add_row("Redis连接:", f"[{redis_style}]{redis_status}[/]")
    
    # 交易对
    status_table.add_row("交易对:", system_status.get('trade_pair', 'Unknown'))
    
    # 订阅的标的数量
    status_table.add_row("订阅标的:", str(system_status.get('subscribed_instruments', 0)))
    
    # API限制器状态
    limiter = api_limiter_status.get('order_rate_limiter', {})
    tokens = limiter.get('tokens', 0)
    capacity = limiter.get('capacity', 1)
    fill_pct = (tokens / capacity) * 100 if capacity > 0 else 0
    
    token_color = "green" if fill_pct > 50 else "yellow" if fill_pct > 20 else "red"
    status_table.add_row("API令牌:", f"[{token_color}]{tokens:.0f}/{capacity} ({fill_pct:.1f}%)[/]")
    
    # 消息统计
    msg_count = connection_stats['messages_received']
    status_table.add_row("收到消息:", str(msg_count))
    
    return Panel(status_table, title="[bold cyan]系统状态[/bold cyan]", border_style="cyan")


def make_positions_panel() -> Panel:
    """生成持仓信息面板"""
    position_table = Table(title="[bold]当前持仓[/bold]", border_style="yellow")
    position_table.add_column("交易对", justify="left", style="cyan")
    position_table.add_column("持仓状态", justify="center", style="green")
    position_table.add_column("方向", justify="center", style="white")
    position_table.add_column("大小", justify="right", style="magenta")
    position_table.add_column("入场成本", justify="right", style="white")
    position_table.add_column("入场基差率", justify="right", style="green")
    position_table.add_column("目标止盈", justify="right", style="blue")
    position_table.add_column("止盈距离", justify="right", style="blue")
    position_table.add_column("持仓时长", justify="center", style="cyan")
    position_table.add_column("未实现PNL", justify="right", style="red")
    position_table.add_column("风险度", justify="center", style="red")

    if not active_positions:
        position_table.add_row("[grey70]无活跃仓位...[/grey70]", "", "", "", "", "", "", "", "", "", "")
    else:
        for pair, pos in active_positions.items():
            # 获取当前基差
            current_basis = market_states.get(pair, {}).get('basis', 0.0)

            # 持仓状态
            pos_status = get_position_status(pos)

            # 方向显示
            pos_type = pos.get('type', 'N/A')
            direction_style = "cyan" if "short_futures_long_spot" in pos_type else "white"
            direction_display = "套利" if "short_futures_long_spot" in pos_type else pos_type

            # 入场成本和基差率
            entry_cost, entry_basis_rate = calculate_entry_info(pair, pos)

            # 目标止盈基差率和距离
            target_profit, profit_distance = calculate_profit_target(pair, pos, current_basis)

            # 持仓时长
            duration = calculate_position_duration(pos)

            # 未实现PNL（bps和USDT双显示）
            upl_display = format_unrealized_pnl(pos, current_basis)

            # 风险度
            risk_level = calculate_risk_level(pos, current_basis)

            position_table.add_row(
                pair,
                pos_status,
                Text(direction_display, style=direction_style),
                f"{pos.get('size', 0):.4f}",
                entry_cost,
                entry_basis_rate,
                target_profit,
                profit_distance,
                duration,
                upl_display,
                risk_level
            )

    return Panel(position_table, title="[bold yellow]持仓管理[/bold yellow]", border_style="yellow")


def get_position_status(pos: Dict) -> str:
    """获取持仓状态"""
    status = pos.get('status', 'unknown')
    open_time = pos.get('open_time')
    close_time = pos.get('close_time')

    if close_time:
        return "[green]已平仓[/green]"
    elif open_time:
        return "[yellow]持仓中[/yellow]"
    else:
        return "[blue]开仓中[/blue]"


def calculate_entry_info(pair: str, pos: Dict) -> tuple:
    """计算入场成本和基差率"""
    from config import get_enabled_trading_pairs

    # 获取配置信息
    pair_config = None
    trading_pairs = get_enabled_trading_pairs()
    for config in trading_pairs:
        if f"{config['spot_id']}-{config['futures_id']}" == pair:
            pair_config = config
            break

    if not pair_config:
        return "N/A", "N/A"

    # 入场成本（现货+期货成本）
    spot_cost = pos.get('spot_entry_price', 0) * pos.get('size', 0)
    futures_cost = pos.get('futures_entry_price', 0) * pos.get('size', 0)
    total_cost = spot_cost + futures_cost

    # 入场基差率
    entry_basis = pos.get('entry_basis', 0)
    entry_basis_pct = (entry_basis * 100) if entry_basis is not None else 0.0

    return f"${total_cost:.5f}", f"{entry_basis_pct:.5f}%"


def calculate_profit_target(pair: str, pos: Dict, current_basis: float) -> tuple:
    """计算目标止盈基差率和距离"""
    from config import BASIS_PROFIT_TARGET

    # 目标止盈基差率
    target_basis = BASIS_PROFIT_TARGET
    target_basis_pct = target_basis * 100

    # 止盈距离（bps）
    entry_basis = pos.get('entry_basis', 0)
    if entry_basis is not None and current_basis is not None:
        distance_bps = abs((target_basis - current_basis) * 10000)
    else:
        distance_bps = 0

    return f"{target_basis_pct:.5f}%", f"{distance_bps:.0f} bps"


def calculate_position_duration(pos: Dict) -> str:
    """计算持仓时长"""
    open_time = pos.get('open_time')
    if not open_time:
        return "N/A"

    try:
        from datetime import datetime
        if isinstance(open_time, str):
            open_dt = datetime.fromisoformat(open_time.replace('Z', '+00:00'))
        else:
            open_dt = datetime.fromtimestamp(open_time)

        duration = datetime.now() - open_dt.replace(tzinfo=None)

        if duration.days > 0:
            return f"{duration.days}天"
        elif duration.seconds > 3600:
            hours = duration.seconds // 3600
            return f"{hours}小时"
        else:
            minutes = duration.seconds // 60
            return f"{minutes}分钟"
    except:
        return "N/A"


def format_unrealized_pnl(pos: Dict, current_basis: float) -> str:
    """格式化未实现PNL（bps和USDT双显示）"""
    # 计算基差变化的bps
    entry_basis = pos.get('entry_basis', 0)
    if entry_basis is not None and current_basis is not None:
        basis_change_bps = (current_basis - entry_basis) * 10000
    else:
        basis_change_bps = 0

    # 计算USDT盈亏
    pnl_usdt = pos.get('unrealized_pnl', 0.0)

    # 颜色编码
    if basis_change_bps > 0 and pnl_usdt > 0:
        style = "green"
        sign = "+"
    elif basis_change_bps < 0 and pnl_usdt < 0:
        style = "red"
        sign = ""
    else:
        style = "yellow"
        sign = "+" if basis_change_bps >= 0 else ""

    return f"[{style}]{sign}{basis_change_bps:.1f} bps (${pnl_usdt:.2f})[/{style}]"


def calculate_risk_level(pos: Dict, current_basis: float) -> str:
    """计算风险度（距离止损线的距离）"""
    from config import BASIS_STOP_LOSS

    entry_basis = pos.get('entry_basis', 0)
    if entry_basis is None or current_basis is None:
        return "N/A"

    # 计算距离止损线的距离
    stop_loss_distance = abs(current_basis - BASIS_STOP_LOSS) * 10000  # bps

    # 风险等级评估
    if stop_loss_distance > 100:  # 大于100bps
        return f"[green]安全 ({stop_loss_distance:.0f}bps)[/green]"
    elif stop_loss_distance > 50:  # 50-100bps
        return f"[yellow]注意 ({stop_loss_distance:.0f}bps)[/yellow]"
    else:  # 小于50bps
        return f"[red]危险 ({stop_loss_distance:.0f}bps)[/red]"


def make_market_state_panel() -> Panel:
    """生成市场状态面板"""
    market_table = Table(title="[bold]实时市场状态[/bold]", border_style="blue")
    market_table.add_column("交易对", style="cyan")
    market_table.add_column("现货价格", justify="right", style="green")
    market_table.add_column("期货价格", justify="right", style="yellow")
    market_table.add_column("基差(%)", justify="right", style="white")
    market_table.add_column("资金费率", justify="right", style="blue")
    market_table.add_column("中轨(%)", justify="right", style="blue")
    market_table.add_column("上轨(%)", justify="right", style="blue")
    market_table.add_column("趋势", justify="center", style="magenta")
    market_table.add_column("数据源", justify="center", style="yellow")
    market_table.add_column("更新时间", justify="center", style="green")
    
    if not market_states:
        market_table.add_row("[grey70]等待市场数据...[/grey70]", "", "", "", "", "", "", "", "", "")
    else:
        for pair, state in market_states.items():
            # 截断长的交易对名称
            display_pair = pair[:10] if len(pair) > 10 else pair

            # 现货和期货价格
            spot_price = state.get('spot_price', 0)
            futures_price = state.get('futures_price', 0)
            spot_display = f"${spot_price:.5f}" if spot_price else "N/A"
            futures_display = f"${futures_price:.5f}" if futures_price else "N/A"

            # 基差颜色编码
            basis = state.get('basis', 0)
            basis_pct = (basis * 100) if basis is not None else 0.0
            if abs(basis_pct) > 2.0:  # 大于2%
                basis_style = "bold red"
            elif abs(basis_pct) > 1.0:  # 大于1%
                basis_style = "bold yellow"
            else:
                basis_style = "green"

            # 资金费率
            funding_rate = state.get('funding_rate', 0)
            funding_rate_pct = (funding_rate * 100) if funding_rate is not None else 0.0
            if funding_rate_pct > 0.01:  # 大于0.01%
                funding_style = "red"
            elif funding_rate_pct < -0.01:  # 小于-0.01%
                funding_style = "green"
            else:
                funding_style = "white"
            funding_display = f"[{funding_style}]{funding_rate_pct:.5f}%[/{funding_style}]"

            # 基差历史趋势（简化版sparkline）
            basis_history = state.get('basis_history', [])
            if len(basis_history) >= 2:
                recent_trend = basis_history[-1] - basis_history[-2]
                if recent_trend > 0.01:
                    trend_indicator = "↗"
                elif recent_trend < -0.01:
                    trend_indicator = "↘"
                else:
                    trend_indicator = "→"
                sparkline_display = f"{trend_indicator}"
            else:
                sparkline_display = "—"

            # 数据源显示
            is_realtime = state.get('is_realtime', False)
            source_display = "[bold green]实时[/]" if is_realtime else "[yellow]定时[/]"

            # 更新时间显示
            last_update = state.get('last_update', '')
            if last_update:
                try:
                    update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                    time_display = update_time.strftime("%H:%M")
                except:
                    time_display = last_update[-5:] if len(last_update) >= 5 else last_update
            else:
                time_display = "--"

            # 安全处理可能为None的值
            bb_middle = state.get('bb_middle', 0)
            bb_upper = state.get('bb_upper', 0)
            bb_middle_pct = (bb_middle * 100) if bb_middle is not None else 0.0
            bb_upper_pct = (bb_upper * 100) if bb_upper is not None else 0.0

            market_table.add_row(
                display_pair,
                spot_display,
                futures_display,
                f"[{basis_style}]{basis_pct:.5f}[/]",
                funding_display,
                f"{bb_middle_pct:.5f}",
                f"{bb_upper_pct:.5f}",
                sparkline_display,
                source_display,
                time_display
            )
    
    return Panel(market_table, title="[bold blue]市场监控[/bold blue]", border_style="blue")


def make_microstructure_panel() -> Panel:
    """生成微观结构面板 - 三层架构：核心压力指标 → 订单簿状态 → 交易流分析"""
    from rich.columns import Columns
    from rich.console import Group
    from rich.progress import Progress, BarColumn, TextColumn
    from rich.align import Align

    # 获取所有配置的交易对
    try:
        from config import get_enabled_trading_pairs
        trading_pairs = get_enabled_trading_pairs()
        configured_pairs = [f"{pair['spot_id']}-{pair['futures_id']}" for pair in trading_pairs]
    except:
        configured_pairs = ["BTC-USDT", "ETH-USDT", "DOGE-USDT"]  # 默认交易对

    panels = []

    for pair_key in configured_pairs:
        # 获取该交易对的微观结构数据
        state = microstructure_states.get(pair_key, {})

        # === 1. 核心压力指标 (Core Pressure Indicators) ===
        obi = state.get('obi', 0.0)
        buy_sell_ratio = state.get('buy_sell_ratio', 1.0)
        micro_confidence = state.get('micro_confidence', 0.0)

        # 计算综合买卖压力
        buy_pressure = max(0, min(100, (obi + 1) * 50))  # OBI转换为0-100
        sell_pressure = 100 - buy_pressure
        volume_pressure = (buy_sell_ratio - 1) * 50 + 50  # 买卖比转换为0-100

        # 压力条显示
        buy_bar = "█" * int(buy_pressure / 10) + "░" * (10 - int(buy_pressure / 10))
        sell_bar = "█" * int(sell_pressure / 10) + "░" * (10 - int(sell_pressure / 10))

        pressure_display = f"""[bold cyan]{pair_key}[/bold cyan]
[green]买压: {buy_bar} {buy_pressure:.1f}%[/green]
[red]卖压: {sell_bar} {sell_pressure:.1f}%[/red]
[yellow]OBI: {obi:+.3f}[/yellow] | [blue]成交量压力: {volume_pressure:.1f}%[/blue]"""

        # === 2. 订单簿状态 (Order Book State) ===
        # 模拟盘口深度（实际应该从订单簿数据获取）
        spread = abs(obi) * 0.01  # 模拟价差
        spread_bps = spread * 10000

        # 简化的盘口深度可视化
        depth_levels = []
        for i in range(5, 0, -1):  # 卖5到卖1
            depth = max(1, int(abs(obi) * 10 * (6-i)))
            bar = "█" * min(depth, 8)
            depth_levels.append(f"卖{i} {bar}")

        depth_levels.append("-" * 15)

        for i in range(1, 6):  # 买1到买5
            depth = max(1, int(buy_sell_ratio * 5 * (6-i)))
            bar = "█" * min(depth, 8)
            depth_levels.append(f"买{i} {bar}")

        orderbook_display = f"""[bold white]订单簿状态[/bold white]
价差: {spread:.5f} ({spread_bps:.1f} bps)
{chr(10).join(depth_levels)}"""

        # === 3. 交易流分析 (Trade Flow Analysis) ===
        micro_signal = state.get('micro_signal', 'neutral')
        obi_signal = state.get('obi_signal', 'neutral')
        flow_signal = state.get('flow_signal', 'neutral')

        # 模拟交易强度和大单追踪
        trade_intensity = int(micro_confidence * 500)  # 模拟每分钟交易笔数

        # 信号颜色映射
        signal_colors = {
            'bullish': 'green', 'buy': 'green', 'inflow': 'green',
            'bearish': 'red', 'sell': 'red', 'outflow': 'red',
            'neutral': 'white'
        }

        micro_color = signal_colors.get(micro_signal, 'white')
        obi_color = signal_colors.get(obi_signal, 'white')
        flow_color = signal_colors.get(flow_signal, 'white')

        flow_display = f"""[bold white]交易流分析[/bold white]
交易强度: {trade_intensity} ticks/min
微观信号: [{micro_color}]{micro_signal.upper()}[/{micro_color}] ({micro_confidence:.2f})
OBI信号: [{obi_color}]{obi_signal.upper()}[/{obi_color}]
资金流: [{flow_color}]{flow_signal.upper()}[/{flow_color}]

[bold yellow]近期大单[/bold yellow]
[green]BUY[/green] 2.5 @ 45001.5
[red]SELL[/red] 3.1 @ 44998.0"""

        # 组合三个部分
        pair_content = Group(
            Align.left(pressure_display),
            "",
            Align.left(orderbook_display),
            "",
            Align.left(flow_display)
        )

        panels.append(Panel(pair_content, title=f"[bold green]{pair_key}[/bold green]", border_style="green", width=35))

    # 如果没有数据，显示等待信息
    if not panels:
        waiting_content = Group(
            Align.center("[bold yellow]等待微观结构数据...[/bold yellow]"),
            "",
            Align.center("[grey70]配置的交易对:[/grey70]"),
            Align.center(f"[cyan]{', '.join(configured_pairs)}[/cyan]")
        )
        panels.append(Panel(waiting_content, title="[bold green]微观结构[/bold green]", border_style="green"))

    # 使用列布局显示多个交易对
    if len(panels) == 1:
        return panels[0]
    else:
        return Panel(Columns(panels, equal=True, expand=True),
                    title="[bold green]微观结构洞察[/bold green]", border_style="green")


def make_alerts_panel() -> Panel:
    """生成警报面板"""
    content = "\n".join(last_alerts) if last_alerts else "[grey70]暂无警报...[/grey70]"
    return Panel(Text(content, style="white"), title="[bold yellow]系统警报[/bold yellow]", border_style="yellow")


def make_trade_events_panel() -> Panel:
    """生成交易事件面板"""
    content = "\n".join(last_trade_events) if last_trade_events else "[grey70]等待交易事件...[/grey70]"
    return Panel(Text(content, style="white"), title="[bold white]关键交易事件[/bold white]", border_style="white")


def update_dashboard():
    """更新整个仪表盘布局"""
    layout["header"].update(make_header())
    
    # 左侧面板
    layout["left"].split_column(
        make_system_status_panel(),
        make_positions_panel(), 
        make_market_state_panel()
    )
    
    # 右侧面板
    layout["right"].split_column(
        make_microstructure_panel(),
        make_alerts_panel(),
        make_trade_events_panel()
    )


def initialize_microstructure_data():
    """初始化所有配置交易对的微观结构数据"""
    global microstructure_states

    try:
        from config import get_enabled_trading_pairs
        trading_pairs = get_enabled_trading_pairs()

        for pair_config in trading_pairs:
            pair_key = f"{pair_config['spot_id']}-{pair_config['futures_id']}"

            if pair_key not in microstructure_states:
                microstructure_states[pair_key] = {
                    'obi': 0.0,
                    'buy_sell_ratio': 1.0,
                    'micro_signal': 'neutral',
                    'micro_confidence': 0.0,
                    'obi_signal': 'neutral',
                    'flow_signal': 'neutral',
                    'last_update': datetime.now().isoformat()
                }
    except Exception as e:
        logging.error(f"初始化微观结构数据失败: {e}")


# 注意：已移除 update_market_data 和 market_data_updater 函数
# 所有市场数据现在都通过Redis消息获取，不再直接调用OKX API


async def main():
    """主程序入口"""
    # 初始化数据结构
    initialize_microstructure_data()

    # 注意：不再初始化OKX连接器，所有数据来自Redis消息
    logging.info("仪表盘启动 - 所有数据来源于Redis消息")

    # 设置基础布局
    layout.split_column(
        Layout(name="header", size=3),
        Layout(ratio=1, name="main")
    )
    layout["main"].split_row(
        Layout(name="left", ratio=2),
        Layout(name="right", ratio=1)
    )
    
    # 创建仪表盘应用
    app = DashboardApp()
    
    # 显示连接进度
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("正在连接Redis服务器...", total=None)

        # 连接Redis
        if not await app.connect_redis():
            console.print("[bold red]Redis连接失败，仪表盘将无法接收实时数据[/bold red]")
            return

        progress.update(task, description="Redis连接已建立，启动仪表盘...")
        await asyncio.sleep(1)

    # 启动后台任务
    app.is_running = True
    redis_task = asyncio.create_task(app._subscribe_to_redis())
    dispatcher_task = asyncio.create_task(app._dispatch_events())
    # 注意：不再启动market_data_updater任务，所有数据来自Redis

    # 启动实时UI
    with Live(layout, screen=True, redirect_stderr=False, refresh_per_second=1/REFRESH_RATE) as live:
        console.print("\n[bold green]🚀 仪表盘启动成功！[/bold green]")
        console.print("[cyan]按 Ctrl+C 退出仪表盘[/cyan]\n")

        try:
            while app.is_running:
                update_dashboard()
                await asyncio.sleep(REFRESH_RATE)

        except KeyboardInterrupt:
            console.print("\n[yellow]正在关闭仪表盘...[/yellow]")
            app.stop()
        except Exception as e:
            console.print(f"\n[bold red]仪表盘发生错误: {e}[/bold red]")
        finally:
            # 清理后台任务
            if not redis_task.done():
                redis_task.cancel()
            if not dispatcher_task.done():
                dispatcher_task.cancel()
            try:
                await asyncio.gather(redis_task, dispatcher_task, return_exceptions=True)
            except Exception:
                pass

            # 清理Redis连接
            if app.pubsub:
                await app.pubsub.unsubscribe()
            if app.redis_client:
                await app.redis_client.aclose()

            # 注意：不再需要清理OKX连接器


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n仪表盘已关闭。")
    except Exception as e:
        print(f"启动仪表盘失败: {e}")