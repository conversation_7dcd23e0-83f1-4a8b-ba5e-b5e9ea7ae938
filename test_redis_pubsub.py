#!/usr/bin/env python3
"""
测试Redis pub/sub功能
"""
import asyncio
import json
import redis.asyncio as redis
from datetime import datetime

async def test_subscriber():
    """测试订阅者"""
    print("启动订阅者...")
    
    # 连接Redis
    redis_client = redis.Redis(
        host='localhost',
        port=6379,
        decode_responses=True
    )
    
    try:
        # 测试连接
        await redis_client.ping()
        print("✅ Redis连接成功")
        
        # 创建订阅
        pubsub = redis_client.pubsub()
        await pubsub.subscribe('trading_bot:market')
        print("📡 已订阅 trading_bot:market 频道")
        
        # 监听消息
        message_count = 0
        while message_count < 5:  # 只接收5条消息
            try:
                message = await asyncio.wait_for(pubsub.get_message(), timeout=2.0)
                if message and message['type'] == 'message':
                    print(f"📨 收到消息: {message['channel']}")
                    data = json.loads(message['data'])
                    print(f"   事件类型: {data.get('event_type')}")
                    print(f"   交易对: {data.get('payload', {}).get('pair_name')}")
                    message_count += 1
            except asyncio.TimeoutError:
                print("⏰ 等待消息超时...")
                continue
            except Exception as e:
                print(f"❌ 处理消息失败: {e}")
                break
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        await redis_client.aclose()
        print("🔌 Redis连接已关闭")

if __name__ == "__main__":
    asyncio.run(test_subscriber())
