#!/usr/bin/env python3
"""
仪表盘系统演示脚本
展示基于Redis消息队列的实时数据发布和订阅功能
"""
import asyncio
import json
import random
import time
from datetime import datetime
from typing import Dict, Any

from redis_publisher import get_redis_publisher


class TradingBotSimulator:
    """模拟交易机器人，发布各种事件到Redis"""
    
    def __init__(self):
        self.publisher = None
        self.is_running = False
        self.trade_count = 0
        self.current_basis = 0.001
        
    async def start(self):
        """启动模拟器"""
        print("🚀 启动交易机器人模拟器...")
        
        # 获取Redis发布器
        self.publisher = await get_redis_publisher()
        print("✅ Redis发布器已连接")
        
        # 发布系统启动事件
        await self.publisher.publish_system_status("Running", {
            "start_time": datetime.now().isoformat(),
            "trade_pair": "BTC-USDT",
            "subscribed_instruments": 6,
            "position_recovered": False
        })
        print("📡 系统启动状态已发布")
        
        self.is_running = True
        
        # 启动各种模拟任务
        tasks = [
            asyncio.create_task(self.simulate_market_data()),
            asyncio.create_task(self.simulate_trading_signals()),
            asyncio.create_task(self.simulate_position_updates()),
            asyncio.create_task(self.simulate_microstructure_data()),
            asyncio.create_task(self.simulate_api_limiter_updates()),
            asyncio.create_task(self.simulate_alerts())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            print("\n⏹️  停止模拟器...")
            self.is_running = False
            for task in tasks:
                task.cancel()
    
    async def simulate_market_data(self):
        """模拟市场数据更新"""
        base_spot_price = 50000.0
        
        while self.is_running:
            try:
                # 模拟价格波动
                spot_price = base_spot_price + random.uniform(-1000, 1000)
                futures_price = spot_price + random.uniform(-100, 100)
                
                # 计算基差
                self.current_basis = (futures_price - spot_price) / spot_price
                
                # 模拟布林带数据
                bb_middle = self.current_basis
                bb_upper = bb_middle + 0.002
                bb_lower = bb_middle - 0.002
                
                market_data = {
                    "basis": self.current_basis,
                    "spot_price": spot_price,
                    "futures_price": futures_price,
                    "bb_middle": bb_middle,
                    "bb_upper": bb_upper,
                    "bb_lower": bb_lower,
                    "strategic_bb_middle": bb_middle * 1.1,
                    "strategic_bb_upper": bb_upper * 1.1,
                    "strategic_bb_lower": bb_lower * 1.1,
                    "tactical_bb_middle": bb_middle * 0.9,
                    "tactical_bb_upper": bb_upper * 0.9,
                    "tactical_bb_lower": bb_lower * 0.9,
                    "update_source": "websocket_realtime",
                    "is_realtime": True,
                    "strategy_state": "monitoring",
                    "data_points": random.randint(50, 200)
                }
                
                await self.publisher.publish_market_data("BTC-USDT", market_data)
                print(f"📊 市场数据: 基差 {self.current_basis*100:.4f}%, 现货 ${spot_price:.2f}")
                
                await asyncio.sleep(2)  # 每2秒更新一次
                
            except Exception as e:
                print(f"❌ 市场数据模拟错误: {e}")
                await asyncio.sleep(1)
    
    async def simulate_trading_signals(self):
        """模拟交易信号"""
        await asyncio.sleep(5)  # 延迟启动
        
        while self.is_running:
            try:
                # 随机生成信号
                if random.random() < 0.3:  # 30%概率生成信号
                    signal_data = {
                        "signal_type": "short_futures_long_spot",
                        "risk_grade": random.choice(["A", "B", "C"]),
                        "confidence": random.uniform(0.6, 0.95),
                        "original_confidence": random.uniform(0.5, 0.8),
                        "net_profit": random.uniform(10, 100),
                        "profit_margin": random.uniform(0.01, 0.05),
                        "base_position_size": random.uniform(0.1, 1.0),
                        "final_position_size": random.uniform(0.1, 1.2),
                        "microstructure_multiplier": random.uniform(0.8, 1.2),
                        "microstructure_available": random.choice([True, False]),
                        "microstructure_signal": random.choice(["bullish", "bearish", "neutral"])
                    }
                    
                    await self.publisher.publish_signal_event("BTC-USDT", signal_data)
                    print(f"🎯 交易信号: {signal_data['signal_type']} | 置信度: {signal_data['confidence']:.2f}")
                
                await asyncio.sleep(random.uniform(8, 15))  # 8-15秒随机间隔
                
            except Exception as e:
                print(f"❌ 交易信号模拟错误: {e}")
                await asyncio.sleep(1)
    
    async def simulate_position_updates(self):
        """模拟仓位更新"""
        await asyncio.sleep(10)  # 延迟启动
        position_open = False
        
        while self.is_running:
            try:
                if not position_open and random.random() < 0.2:  # 20%概率开仓
                    self.trade_count += 1
                    position_data = {
                        "position_id": f"pos_demo_{self.trade_count}",
                        "position_type": "short_futures_long_spot",
                        "entry_basis": self.current_basis,
                        "entry_spot_price": 50000 + random.uniform(-500, 500),
                        "entry_futures_price": 50050 + random.uniform(-500, 500),
                        "final_position_size": random.uniform(0.5, 1.5),
                        "unrealized_pnl": 0.0,
                        "open_time": datetime.now().isoformat()
                    }
                    
                    await self.publisher.publish_position_event(
                        "position_opened", "BTC-USDT", position_data
                    )
                    position_open = True
                    print(f"🔓 开仓: {position_data['position_id']} | 基差: {self.current_basis*100:.4f}%")
                
                elif position_open and random.random() < 0.15:  # 15%概率平仓
                    realized_pnl = random.uniform(-50, 150)
                    position_data = {
                        "position_id": f"pos_demo_{self.trade_count}",
                        "realized_pnl": realized_pnl,
                        "close_reason": "profit_target" if realized_pnl > 0 else "stop_loss",
                        "close_time": datetime.now().isoformat()
                    }
                    
                    await self.publisher.publish_position_event(
                        "position_closed", "BTC-USDT", position_data
                    )
                    position_open = False
                    pnl_emoji = "💚" if realized_pnl > 0 else "❤️"
                    print(f"🔒 平仓: {pnl_emoji} 盈亏: ${realized_pnl:.2f}")
                
                await asyncio.sleep(random.uniform(10, 20))
                
            except Exception as e:
                print(f"❌ 仓位模拟错误: {e}")
                await asyncio.sleep(1)
    
    async def simulate_microstructure_data(self):
        """模拟微观结构数据"""
        await asyncio.sleep(3)
        
        while self.is_running:
            try:
                micro_data = {
                    "obi": random.uniform(-0.5, 0.5),
                    "buy_sell_ratio": random.uniform(0.5, 2.0),
                    "micro_signal": random.choice(["bullish", "bearish", "neutral"]),
                    "micro_confidence": random.uniform(0.3, 0.9)
                }
                
                await self.publisher.publish_microstructure_data("BTC-USDT", micro_data)
                print(f"🧠 微观结构: {micro_data['micro_signal']} | OBI: {micro_data['obi']:.3f}")
                
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"❌ 微观结构模拟错误: {e}")
                await asyncio.sleep(1)
    
    async def simulate_api_limiter_updates(self):
        """模拟API限制器状态"""
        tokens = 10
        capacity = 10
        
        while self.is_running:
            try:
                # 模拟令牌消耗和补充
                tokens = max(0, tokens - random.randint(0, 2))  # 消耗
                tokens = min(capacity, tokens + random.uniform(0.5, 1.5))  # 补充
                
                limiter_data = {
                    "order_rate_limiter": {
                        "tokens": tokens,
                        "capacity": capacity,
                        "refill_rate": 0.1
                    }
                }
                
                await self.publisher.publish_api_limiter_status(limiter_data)
                print(f"🔄 API令牌: {tokens:.1f}/{capacity} ({tokens/capacity*100:.1f}%)")
                
                await asyncio.sleep(3)
                
            except Exception as e:
                print(f"❌ API限制器模拟错误: {e}")
                await asyncio.sleep(1)
    
    async def simulate_alerts(self):
        """模拟系统警报"""
        await asyncio.sleep(12)
        
        alert_messages = [
            ("INFO", "系统运行正常"),
            ("INFO", "市场数据更新正常"),
            ("WARNING", "基差波动较大"),
            ("WARNING", "API调用频率接近限制"),
            ("ERROR", "WebSocket连接短暂中断"),
            ("ERROR", "订单簿数据延迟"),
            ("CRITICAL", "风险控制触发"),
        ]
        
        while self.is_running:
            try:
                if random.random() < 0.4:  # 40%概率生成警报
                    level, message = random.choice(alert_messages)
                    
                    await self.publisher.publish_alert(level, message, {
                        "component": "demo_simulator",
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    level_emoji = {"INFO": "ℹ️", "WARNING": "⚠️", "ERROR": "❌", "CRITICAL": "🔥"}
                    print(f"🚨 警报 {level_emoji.get(level, '📢')} [{level}] {message}")
                
                await asyncio.sleep(random.uniform(15, 30))
                
            except Exception as e:
                print(f"❌ 警报模拟错误: {e}")
                await asyncio.sleep(1)


async def main():
    """主函数"""
    print("=" * 60)
    print("🎮 交易仪表盘系统演示")
    print("=" * 60)
    print()
    print("📋 使用说明:")
    print("1. 此脚本模拟交易机器人发布各种实时事件到Redis")
    print("2. 同时运行 'python trading_dashboard.py' 查看仪表盘")
    print("3. 按 Ctrl+C 停止模拟器")
    print()
    print("🚀 启动中...")
    print()
    
    simulator = TradingBotSimulator()
    
    try:
        await simulator.start()
    except KeyboardInterrupt:
        print("\n")
        print("🛑 演示结束")
        if simulator.publisher:
            await simulator.publisher.disconnect()
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())