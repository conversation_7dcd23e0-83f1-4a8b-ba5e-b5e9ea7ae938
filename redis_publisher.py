"""
Redis发布器模块 - 为仪表盘提供实时消息发布功能
支持高性能的异步消息发布和连接重试机制
"""
import json
import logging
import asyncio
import time
from typing import Dict, Any, Optional, Union
from datetime import datetime
import redis.asyncio as redis
from contextlib import asynccontextmanager


class RedisPublisher:
    """Redis消息发布器 - 支持异步发布和连接管理"""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, 
                 password: Optional[str] = None, db: int = 0,
                 max_retries: int = 3, retry_delay: float = 1.0):
        """
        初始化Redis发布器
        
        Args:
            host: Redis服务器地址
            port: Redis端口
            password: Redis密码
            db: Redis数据库编号
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        self.host = host
        self.port = port
        self.password = password
        self.db = db
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        self.redis_client: Optional[redis.Redis] = None
        self.is_connected = False
        self.logger = logging.getLogger(__name__)
        
        # 频道配置
        self.channels = {
            'system': 'trading_bot:system',
            'positions': 'trading_bot:positions', 
            'market': 'trading_bot:market',
            'microstructure': 'trading_bot:microstructure',
            'alerts': 'trading_bot:alerts'
        }
        
        # 统计信息
        self.stats = {
            'messages_sent': 0,
            'send_errors': 0,
            'connection_failures': 0,
            'last_send_time': None
        }
    
    async def connect(self) -> bool:
        """
        连接到Redis服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.redis_client = redis.Redis(
                host=self.host,
                port=self.port,
                password=self.password,
                db=self.db,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis_client.ping()
            self.is_connected = True
            self.logger.info(f"✅ Redis连接成功: {self.host}:{self.port}")
            return True
            
        except Exception as e:
            self.is_connected = False
            self.stats['connection_failures'] += 1
            self.logger.error(f"❌ Redis连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            try:
                await self.redis_client.aclose()
                self.is_connected = False
                self.logger.info("Redis连接已断开")
            except Exception as e:
                self.logger.error(f"Redis断开连接失败: {e}")
    
    async def _ensure_connected(self) -> bool:
        """确保Redis连接可用"""
        if not self.is_connected or not self.redis_client:
            return await self.connect()
        
        try:
            await self.redis_client.ping()
            return True
        except Exception:
            self.is_connected = False
            return await self.connect()
    
    async def publish_event(self, channel_name: str, event_type: str, 
                          payload: Dict[str, Any], trace_id: Optional[str] = None) -> bool:
        """
        发布事件到指定频道
        
        Args:
            channel_name: 频道名称 ('system', 'positions', 'market', 'microstructure', 'alerts')
            event_type: 事件类型
            payload: 事件数据
            trace_id: 追踪ID
            
        Returns:
            bool: 发布是否成功
        """
        if not await self._ensure_connected():
            return False
        
        try:
            # 构建消息结构
            message = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'payload': payload,
                'trace_id': trace_id,
                'source': 'arbitrage_bot'
            }
            
            # 获取真实频道名
            channel = self.channels.get(channel_name, channel_name)
            
            # 发布消息
            message_json = json.dumps(message, ensure_ascii=False, separators=(',', ':'))
            await self.redis_client.publish(channel, message_json)
            
            # 更新统计
            self.stats['messages_sent'] += 1
            self.stats['last_send_time'] = datetime.now()
            
            self.logger.debug(f"📤 发布事件到 {channel}: {event_type}")
            return True
            
        except Exception as e:
            self.stats['send_errors'] += 1
            self.logger.error(f"❌ 发布事件失败 {channel_name}:{event_type} - {e}")
            return False
    
    async def publish_system_status(self, status: str, details: Dict[str, Any] = None,
                                  trace_id: Optional[str] = None) -> bool:
        """发布系统状态事件"""
        payload = {
            'status': status,
            'details': details or {},
            'timestamp': datetime.now().isoformat()
        }
        return await self.publish_event('system', 'system_status', payload, trace_id)
    
    async def publish_position_event(self, event_type: str, pair_name: str, 
                                   position_data: Dict[str, Any],
                                   trace_id: Optional[str] = None) -> bool:
        """发布仓位相关事件"""
        payload = {
            'pair_name': pair_name,
            'position_data': position_data,
            'timestamp': datetime.now().isoformat()
        }
        return await self.publish_event('positions', event_type, payload, trace_id)
    
    async def publish_market_data(self, pair_name: str, market_state: Dict[str, Any],
                                trace_id: Optional[str] = None) -> bool:
        """发布市场数据事件"""
        payload = {
            'pair_name': pair_name,
            'market_state': market_state,
            'timestamp': datetime.now().isoformat()
        }
        return await self.publish_event('market', 'market_state', payload, trace_id)
    
    async def publish_signal_event(self, pair_name: str, signal_data: Dict[str, Any],
                                 trace_id: Optional[str] = None) -> bool:
        """发布交易信号事件"""
        payload = {
            'pair_name': pair_name,
            'signal_data': signal_data,
            'timestamp': datetime.now().isoformat()
        }
        return await self.publish_event('market', 'signal_generated', payload, trace_id)
    
    async def publish_microstructure_data(self, pair_name: str, micro_data: Dict[str, Any],
                                        trace_id: Optional[str] = None) -> bool:
        """发布微观结构数据事件"""
        payload = {
            'pair_name': pair_name,
            'micro_data': micro_data,
            'timestamp': datetime.now().isoformat()
        }
        return await self.publish_event('microstructure', 'microstructure_update', payload, trace_id)
    
    async def publish_alert(self, level: str, message: str, details: Dict[str, Any] = None,
                          trace_id: Optional[str] = None) -> bool:
        """发布警报事件"""
        payload = {
            'level': level.upper(),  # INFO, WARNING, ERROR, CRITICAL
            'message': message,
            'details': details or {},
            'timestamp': datetime.now().isoformat()
        }
        return await self.publish_event('alerts', 'alert', payload, trace_id)
    
    async def publish_trade_event(self, event_type: str, pair_name: str, 
                                trade_data: Dict[str, Any],
                                trace_id: Optional[str] = None) -> bool:
        """发布交易事件"""
        payload = {
            'pair_name': pair_name,
            'trade_data': trade_data,
            'timestamp': datetime.now().isoformat()
        }
        return await self.publish_event('positions', event_type, payload, trace_id)
    
    async def publish_api_limiter_status(self, limiter_data: Dict[str, Any],
                                       trace_id: Optional[str] = None) -> bool:
        """发布API限制器状态"""
        payload = {
            'limiter_data': limiter_data,
            'timestamp': datetime.now().isoformat()
        }
        return await self.publish_event('system', 'api_limiter_status', payload, trace_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取发布器统计信息"""
        return {
            'connection_status': self.is_connected,
            'redis_config': f"{self.host}:{self.port}",
            'messages_sent': self.stats['messages_sent'],
            'send_errors': self.stats['send_errors'],
            'connection_failures': self.stats['connection_failures'],
            'last_send_time': self.stats['last_send_time'].isoformat() if self.stats['last_send_time'] else None,
            'channels': list(self.channels.keys())
        }
    
    @asynccontextmanager
    async def connection_context(self):
        """连接上下文管理器"""
        try:
            await self.connect()
            yield self
        finally:
            await self.disconnect()
    
    def __repr__(self):
        return (
            f"RedisPublisher("
            f"host={self.host}, "
            f"port={self.port}, "
            f"connected={self.is_connected}, "
            f"sent={self.stats['messages_sent']}"
            f")"
        )


class SingletonRedisPublisher:
    """Redis发布器单例 - 确保全局唯一实例"""
    
    _instance: Optional[RedisPublisher] = None
    _initialized = False
    
    @classmethod
    async def get_instance(cls, **kwargs) -> RedisPublisher:
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = RedisPublisher(**kwargs)
            await cls._instance.connect()
            cls._initialized = True
        return cls._instance
    
    @classmethod
    async def cleanup(cls):
        """清理单例实例"""
        if cls._instance:
            await cls._instance.disconnect()
            cls._instance = None
            cls._initialized = False


# 便捷函数
async def get_redis_publisher(**kwargs) -> RedisPublisher:
    """获取Redis发布器实例"""
    return await SingletonRedisPublisher.get_instance(**kwargs)


async def cleanup_redis_publisher():
    """清理Redis发布器"""
    await SingletonRedisPublisher.cleanup()