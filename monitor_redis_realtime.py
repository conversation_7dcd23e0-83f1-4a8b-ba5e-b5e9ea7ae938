#!/usr/bin/env python3
"""
实时监控Redis数据流
专门用于调试仪表盘数据更新问题
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
import time

async def monitor_all_channels():
    """监控所有Redis频道的实时数据"""
    print("🔍 实时监控所有Redis频道...")
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        pubsub = redis_client.pubsub()
        
        # 订阅所有频道
        channels = [
            'trading_bot:market',
            'trading_bot:positions', 
            'trading_bot:system',
            'trading_bot:microstructure',
            'trading_bot:alerts'
        ]
        
        for channel in channels:
            await pubsub.subscribe(channel)
            print(f"📡 已订阅 {channel}")
        
        print(f"\n⏰ 开始实时监控...")
        print("=" * 80)
        
        message_counts = {channel: 0 for channel in channels}
        start_time = time.time()
        
        while True:
            try:
                message = await asyncio.wait_for(pubsub.get_message(), timeout=1.0)
                if message and message['type'] == 'message':
                    channel = message['channel']
                    message_counts[channel] += 1
                    
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    
                    try:
                        data = json.loads(message['data'])
                        event_type = data.get('event_type', 'unknown')
                        
                        print(f"[{timestamp}] 📨 {channel} -> {event_type}")
                        
                        # 详细显示market数据
                        if channel == 'trading_bot:market' and event_type == 'market_state':
                            payload = data.get('payload', {})
                            pair_name = payload.get('pair_name', 'Unknown')
                            market_state = payload.get('market_state', {})
                            
                            spot_price = market_state.get('spot_price', 0)
                            futures_price = market_state.get('futures_price', 0)
                            basis = market_state.get('basis', 0)
                            funding_rate = market_state.get('funding_rate', 0)
                            bb_middle = market_state.get('bb_middle')
                            bb_upper = market_state.get('bb_upper')
                            
                            print(f"    📈 {pair_name}:")
                            print(f"      现货: ${spot_price:.5f}")
                            print(f"      期货: ${futures_price:.5f}")
                            print(f"      基差: {basis*100:.5f}%")
                            print(f"      资金费率: {funding_rate*100:.5f}%")
                            print(f"      中轨: {bb_middle*100:.5f}%" if bb_middle else "      中轨: None")
                            print(f"      上轨: {bb_upper*100:.5f}%" if bb_upper else "      上轨: None")
                        
                        # 显示其他频道的基本信息
                        elif channel != 'trading_bot:market':
                            payload = data.get('payload', {})
                            if isinstance(payload, dict):
                                keys = list(payload.keys())[:3]  # 显示前3个键
                                print(f"    📋 数据键: {keys}")
                        
                    except json.JSONDecodeError:
                        print(f"[{timestamp}] ❌ {channel} -> JSON解析失败")
                    except Exception as e:
                        print(f"[{timestamp}] ⚠️ {channel} -> 处理错误: {e}")
                
                # 每10秒显示统计
                if int(time.time() - start_time) % 10 == 0 and int(time.time() - start_time) > 0:
                    print(f"\n📊 [{int(time.time() - start_time)}秒] 消息统计:")
                    for channel, count in message_counts.items():
                        status = "✅" if count > 0 else "❌"
                        print(f"   {status} {channel}: {count} 条消息")
                    print("-" * 80)
                        
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                break
        
        await pubsub.unsubscribe()
        await redis_client.aclose()
        
    except Exception as e:
        print(f"❌ 监控失败: {e}")

async def check_bot_logs_realtime():
    """实时检查机器人日志中的Redis相关信息"""
    print("\n🔍 检查机器人日志中的Redis发布信息...")
    
    import subprocess
    import os
    
    if os.path.exists('arbitrage_bot_structured.log'):
        try:
            # 获取最后20行包含Redis的日志
            result = subprocess.run([
                'tail', '-20', 'arbitrage_bot_structured.log'
            ], capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                redis_lines = [line for line in lines if any(keyword in line.lower() for keyword in ['redis', 'publish', '发布'])]
                
                if redis_lines:
                    print("📄 最近的Redis相关日志:")
                    for line in redis_lines[-5:]:  # 显示最后5条
                        if line.strip():
                            print(f"   {line}")
                else:
                    print("❌ 最近20行日志中没有Redis相关信息")
            else:
                print(f"❌ 读取日志失败: {result.stderr}")
                
        except Exception as e:
            print(f"❌ 检查日志失败: {e}")
    else:
        print("❌ arbitrage_bot_structured.log 不存在")

async def main():
    """主监控流程"""
    print("🧪 Redis数据流实时监控")
    print("=" * 80)
    
    # 检查机器人日志
    await check_bot_logs_realtime()
    
    print("\n" + "=" * 80)
    
    # 开始实时监控
    try:
        await monitor_all_channels()
    except KeyboardInterrupt:
        print("\n\n⏹️ 监控已停止")
    except Exception as e:
        print(f"\n❌ 监控异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
