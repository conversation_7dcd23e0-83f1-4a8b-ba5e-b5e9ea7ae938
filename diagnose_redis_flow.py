#!/usr/bin/env python3
"""
诊断Redis数据流问题
检查交易机器人是否在发布数据，仪表盘是否在接收数据
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
import time

async def check_redis_connection():
    """检查Redis连接"""
    print("🔍 检查Redis连接...")
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        await redis_client.ping()
        print("✅ Redis连接正常")
        
        # 检查Redis信息
        info = await redis_client.info()
        print(f"📊 Redis版本: {info.get('redis_version', 'Unknown')}")
        print(f"📊 连接的客户端数: {info.get('connected_clients', 0)}")
        
        await redis_client.close()
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

async def monitor_all_channels():
    """监控所有Redis频道"""
    print("\n🔍 监控所有Redis频道...")
    
    channels = [
        'trading_bot:system',
        'trading_bot:positions', 
        'trading_bot:market',
        'trading_bot:microstructure',
        'trading_bot:alerts'
    ]
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        pubsub = redis_client.pubsub()
        
        # 订阅所有频道
        await pubsub.subscribe(*channels)
        print(f"📡 已订阅频道: {channels}")
        
        print("\n⏰ 开始监控（30秒）...")
        start_time = time.time()
        message_count = 0
        channel_stats = {channel: 0 for channel in channels}
        
        while time.time() - start_time < 30:  # 监控30秒
            try:
                message = await asyncio.wait_for(pubsub.get_message(), timeout=1.0)
                if message and message['type'] == 'message':
                    message_count += 1
                    channel = message['channel']
                    channel_stats[channel] += 1
                    
                    try:
                        data = json.loads(message['data'])
                        event_type = data.get('event_type', 'unknown')
                        timestamp = data.get('timestamp', 'no_timestamp')
                        
                        print(f"📨 [{message_count:3d}] {channel} -> {event_type} @ {timestamp}")
                        
                        # 如果是市场数据，显示详细信息
                        if event_type == 'market_state':
                            payload = data.get('payload', {})
                            pair_name = payload.get('pair_name', 'Unknown')
                            market_state = payload.get('market_state', {})
                            
                            spot_price = market_state.get('spot_price', 0)
                            futures_price = market_state.get('futures_price', 0)
                            basis = market_state.get('basis', 0)
                            funding_rate = market_state.get('funding_rate', 0)
                            bb_middle = market_state.get('bb_middle', 0)
                            
                            print(f"     📈 {pair_name}: 现货=${spot_price:.5f}, 期货=${futures_price:.5f}")
                            print(f"     📊 基差={basis*100:.5f}%, 资金费率={funding_rate*100:.5f}%, 中轨={bb_middle*100:.5f}%")
                        
                    except json.JSONDecodeError:
                        print(f"📨 [{message_count:3d}] {channel} -> 无法解析JSON")
                    except Exception as e:
                        print(f"📨 [{message_count:3d}] {channel} -> 解析错误: {e}")
                        
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                break
        
        await pubsub.unsubscribe()
        await redis_client.close()
        
        print(f"\n📊 监控结果:")
        print(f"   总消息数: {message_count}")
        for channel, count in channel_stats.items():
            status = "✅" if count > 0 else "❌"
            print(f"   {status} {channel}: {count} 条消息")
        
        return message_count > 0
        
    except Exception as e:
        print(f"❌ 监控失败: {e}")
        return False

async def test_dashboard_subscription():
    """测试仪表盘订阅逻辑"""
    print("\n🔍 测试仪表盘订阅逻辑...")
    
    # 模拟仪表盘的订阅方式
    REDIS_CHANNELS = {
        'system': 'trading_bot:system',
        'positions': 'trading_bot:positions', 
        'market': 'trading_bot:market',
        'microstructure': 'trading_bot:microstructure',
        'alerts': 'trading_bot:alerts'
    }
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        pubsub = redis_client.pubsub()
        
        # 使用仪表盘相同的订阅方式
        channels_to_subscribe = list(REDIS_CHANNELS.values())
        await pubsub.subscribe(*channels_to_subscribe)
        
        print(f"📡 仪表盘订阅方式测试: {channels_to_subscribe}")
        
        message_count = 0
        start_time = time.time()
        
        while time.time() - start_time < 10:  # 测试10秒
            try:
                message = await asyncio.wait_for(pubsub.get_message(), timeout=1.0)
                if message and message['type'] == 'message':
                    message_count += 1
                    print(f"📨 仪表盘收到消息 #{message_count}: {message['channel']}")
                    
            except asyncio.TimeoutError:
                continue
        
        await pubsub.unsubscribe()
        await redis_client.close()
        
        if message_count > 0:
            print(f"✅ 仪表盘订阅测试成功，收到 {message_count} 条消息")
        else:
            print("❌ 仪表盘订阅测试失败，没有收到任何消息")
        
        return message_count > 0
        
    except Exception as e:
        print(f"❌ 仪表盘订阅测试失败: {e}")
        return False

async def send_test_data():
    """发送测试数据"""
    print("\n🧪 发送测试数据...")
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        
        # 发送测试市场数据
        test_market_data = {
            "event_type": "market_state",
            "payload": {
                "pair_name": "BTC-USDT-BTC-USDT-SWAP",
                "market_state": {
                    "basis": 0.001234,
                    "spot_price": 45000.12345,
                    "futures_price": 45055.67890,
                    "funding_rate": 0.000123,
                    "bb_middle": 0.001100,
                    "bb_upper": 0.001500,
                    "bb_lower": 0.000700,
                    "update_source": "test_diagnostic",
                    "is_realtime": True
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await redis_client.publish('trading_bot:market', json.dumps(test_market_data))
        print("📤 已发送测试市场数据到 trading_bot:market")
        
        # 发送测试系统状态
        test_system_data = {
            "event_type": "system_status",
            "payload": {
                "status": "Running",
                "details": {
                    "test_mode": True,
                    "diagnostic_time": datetime.now().isoformat()
                }
            },
            "timestamp": datetime.now().isoformat()
        }
        
        await redis_client.publish('trading_bot:system', json.dumps(test_system_data))
        print("📤 已发送测试系统数据到 trading_bot:system")
        
        await redis_client.close()
        print("✅ 测试数据发送完成")
        
    except Exception as e:
        print(f"❌ 发送测试数据失败: {e}")

async def check_trading_bot_status():
    """检查交易机器人状态"""
    print("\n🔍 检查交易机器人状态...")
    
    import subprocess
    import os
    
    try:
        # 检查进程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if 'arbitrage_bot.py' in result.stdout:
            print("✅ 交易机器人进程正在运行")
        else:
            print("❌ 交易机器人进程未运行")
        
        # 检查日志文件
        log_files = [
            'arbitrage_bot_structured.log',
            'arbitrage_bot.log'
        ]
        
        for log_file in log_files:
            if os.path.exists(log_file):
                # 检查最近的日志
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        last_line = lines[-1]
                        print(f"📄 {log_file} 最后一行: {last_line.strip()}")
                    else:
                        print(f"📄 {log_file} 为空")
            else:
                print(f"❌ {log_file} 不存在")
                
    except Exception as e:
        print(f"❌ 检查交易机器人状态失败: {e}")

async def main():
    """主诊断流程"""
    print("🩺 Redis数据流诊断工具")
    print("=" * 60)
    
    # 1. 检查Redis连接
    redis_ok = await check_redis_connection()
    if not redis_ok:
        print("❌ Redis连接失败，无法继续诊断")
        return
    
    # 2. 检查交易机器人状态
    await check_trading_bot_status()
    
    # 3. 发送测试数据
    await send_test_data()
    
    # 4. 监控所有频道
    has_data = await monitor_all_channels()
    
    # 5. 测试仪表盘订阅
    dashboard_ok = await test_dashboard_subscription()
    
    print("\n" + "=" * 60)
    print("📋 诊断结果总结:")
    print(f"   Redis连接: {'✅' if redis_ok else '❌'}")
    print(f"   数据流检测: {'✅' if has_data else '❌'}")
    print(f"   仪表盘订阅: {'✅' if dashboard_ok else '❌'}")
    
    if not has_data:
        print("\n💡 建议:")
        print("   1. 确保交易机器人正在运行: python arbitrage_bot.py")
        print("   2. 检查交易机器人日志是否有错误")
        print("   3. 确认Redis发布器初始化成功")
        print("   4. 检查WebSocket连接是否正常")
    
    if not dashboard_ok:
        print("\n💡 仪表盘问题建议:")
        print("   1. 检查仪表盘Redis连接配置")
        print("   2. 确认频道名称匹配")
        print("   3. 检查异步任务是否正常启动")

if __name__ == "__main__":
    asyncio.run(main())
