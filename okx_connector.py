"""
OKX异步API连接器 - 高性能WebSocket和REST API客户端
"""
import asyncio
import json
import hmac
import hashlib
import base64
import time
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Callable, List
import websockets
import aiohttp
from websockets.exceptions import ConnectionClosed, WebSocketException
from log_utils import get_structured_logger, KPICalculator


class TokenBucketRateLimiter:
    """令牌桶算法速率限制器 - 用于OKX API请求速率控制"""
    
    def __init__(self, capacity: int, refill_rate: float, initial_tokens: int = None):
        """
        初始化令牌桶
        
        Args:
            capacity: 桶容量（最大令牌数）
            refill_rate: 令牌填充速率（令牌/秒）
            initial_tokens: 初始令牌数（默认为满桶）
        """
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = initial_tokens if initial_tokens is not None else capacity
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int = 1) -> bool:
        """
        获取令牌（非阻塞）
        
        Args:
            tokens: 需要的令牌数量
            
        Returns:
            bool: 是否成功获取令牌
        """
        async with self._lock:
            self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    async def wait_for_tokens(self, tokens: int = 1, timeout: float = 30.0) -> bool:
        """
        等待获取令牌（阻塞，直到有足够令牌或超时）
        
        Args:
            tokens: 需要的令牌数量
            timeout: 最大等待时间（秒）
            
        Returns:
            bool: 是否成功获取令牌
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if await self.acquire(tokens):
                return True
            
            # 计算下次有足够令牌的时间
            async with self._lock:
                self._refill()
                needed_tokens = tokens - self.tokens
                if needed_tokens > 0:
                    wait_time = min(needed_tokens / self.refill_rate, 0.1)
                    await asyncio.sleep(wait_time)
                else:
                    await asyncio.sleep(0.01)  # 短暂等待
        
        return False
    
    def _refill(self):
        """填充令牌（内部方法，调用时需要持有锁）"""
        now = time.time()
        elapsed = now - self.last_refill
        
        if elapsed > 0:
            new_tokens = elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + new_tokens)
            self.last_refill = now
    
    def get_status(self) -> Dict[str, Any]:
        """获取令牌桶状态"""
        return {
            "tokens": self.tokens,
            "capacity": self.capacity,
            "refill_rate": self.refill_rate,
            "fill_percentage": (self.tokens / self.capacity) * 100
        }


class OKXConnector:
    """OKX异步连接器 - 双通道实时数据总线"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_structured_logger(__name__)
        
        # HTTP客户端会话
        self.http_session: Optional[aiohttp.ClientSession] = None
        
        # 双通道WebSocket连接
        self.ws_public = None
        self.ws_private = None
        
        # 连接状态管理
        self.is_public_connected = False
        self.is_private_connected = False
        self.is_private_authenticated = False
        self.reconnect_attempts = {'public': 0, 'private': 0}
        self.max_reconnect_attempts = 10
        
        # 事件分发器系统
        self.event_callbacks: Dict[str, List[Callable]] = {
            'ticker': [],
            'books': [],
            'trades': [],          # 新增逐笔成交事件
            'funding-rate': [],
            'account': [],
            'orders': [],
            'positions': [],
            'balance': []
        }
        
        # 消息处理任务
        self._public_message_task = None
        self._private_message_task = None
        
        # WebSocket订单状态跟踪系统
        self.pending_orders: Dict[str, asyncio.Event] = {}  # order_id -> completion_event
        self.order_results: Dict[str, Dict[str, Any]] = {}  # order_id -> order_result
        self._order_lock = asyncio.Lock()
        
        # 合约规格缓存 - 动态获取所有交易对的合约规格
        self.instrument_specs: Dict[str, Dict[str, Any]] = {}  # 缓存所有交易对的合约规格
        self.instrument_specs_initialized = False
        
        # API速率限制器 - 基于OKX限制：20次/2秒
        self.order_rate_limiter = TokenBucketRateLimiter(
            capacity=20,        # 桶容量20个令牌
            refill_rate=10.0    # 每秒补充10个令牌 (20/2s)
        )
        self.query_rate_limiter = TokenBucketRateLimiter(
            capacity=30,        # 查询API限制相对宽松
            refill_rate=15.0    # 每秒补充15个令牌
        )
        
        # WebSocket实时盘口数据缓存 - 零延迟盘口获取
        self.orderbook_cache: Dict[str, Dict[str, Any]] = {}  # inst_id -> orderbook_data
        self.orderbook_lock = asyncio.Lock()
        self.orderbook_subscriptions: set = set()  # 已订阅的交易对
        
        # OKX服务器时间同步机制 - 防止时钟偏差导致的签名失败
        self.server_time_offset: float = 0.0  # 本地时间与服务器时间的偏差(毫秒)
        self.last_time_sync: float = 0  # 上次时间同步的时间戳
        self.time_sync_interval: float = 300  # 5分钟同步一次
        self.time_sync_lock = asyncio.Lock()
        
        # API基础URL
        self.base_url = "https://www.okx.com" if not config.USE_SANDBOX else "https://www.okx.com"
        
        # WebSocket URLs
        self.ws_public_url = "wss://ws.okx.com:8443/ws/v5/public" if not config.USE_SANDBOX else "wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999"
        self.ws_private_url = "wss://ws.okx.com:8443/ws/v5/private" if not config.USE_SANDBOX else "wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999"
        
        # 设置代理
        self.proxy = None
        if config.PROXY_CONFIG.get("ENABLED"):
            self.proxy = config.PROXY_CONFIG.get("URL")
    
    async def start(self):
        """启动双通道连接器"""
        try:
            # 创建HTTP会话
            connector = aiohttp.TCPConnector(
                limit=100,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            timeout = aiohttp.ClientTimeout(total=30)
            self.http_session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'AsyncArbitrageBot/1.0'
                }
            )
            
            # 启动双通道WebSocket连接
            await self._connect_dual_channels()
            
            # 初始化服务器时间同步
            self.logger.info("🕐 初始化服务器时间同步...")
            if await self.sync_server_time(force=True):
                self.logger.info(f"✅ 时钟同步完成，偏差: {self.server_time_offset:.1f}ms")
            else:
                self.logger.warning("⚠️ 时钟同步失败，将使用本地时间（可能影响API请求）")
            
            # 注册内部订单状态跟踪器
            self.register_event_callback('orders', self._handle_order_completion)
            
            self.logger.info("OKX双通道连接器启动成功")
            
        except Exception as e:
            self.logger.error(f"连接器启动失败: {e}")
            raise
    
    async def stop(self):
        """停止双通道连接器"""
        try:
            # 停止消息处理任务
            if self._public_message_task and not self._public_message_task.done():
                self._public_message_task.cancel()
                try:
                    await self._public_message_task
                except asyncio.CancelledError:
                    pass
            
            if self._private_message_task and not self._private_message_task.done():
                self._private_message_task.cancel()
                try:
                    await self._private_message_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭双通道WebSocket连接
            if self.ws_public:
                await self.ws_public.close()
            if self.ws_private:
                await self.ws_private.close()
            
            # 关闭HTTP会话和连接器
            if self.http_session and not self.http_session.closed:
                await self.http_session.close()
                # 等待连接器完全关闭
                if hasattr(self.http_session, '_connector'):
                    await asyncio.sleep(0.25)  # 给连接器时间清理
            
            self.is_public_connected = False
            self.is_private_connected = False
            self.is_private_authenticated = False
            self.logger.info("OKX双通道连接器已停止")
            
        except Exception as e:
            self.logger.error(f"停止连接器时发生错误: {e}")
    
    def _generate_signature(self, timestamp: str, method: str, request_path: str, body: str = '') -> str:
        """生成API签名"""
        message = timestamp + method.upper() + request_path + body
        signature = base64.b64encode(
            hmac.new(
                self.config.API_SECRET.encode('utf-8'),
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        return signature
    
    async def get_server_time(self) -> Optional[int]:
        """获取OKX服务器时间 - 用于时钟同步"""
        try:
            if not self.http_session:
                raise RuntimeError("HTTP会话未初始化")
            
            request_path = "/api/v5/public/time"
            
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    server_time = int(result['data'][0]['ts'])
                    self.logger.debug(f"🕐 获取服务器时间: {server_time}")
                    return server_time
                else:
                    self.logger.warning(f"获取服务器时间失败: {result}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"获取服务器时间异常: {e}")
            return None
    
    async def sync_server_time(self, force: bool = False) -> bool:
        """同步服务器时间 - 计算并校准时钟偏差"""
        async with self.time_sync_lock:
            current_time = time.time()
            
            # 检查是否需要同步
            if not force and (current_time - self.last_time_sync) < self.time_sync_interval:
                return True
            
            try:
                # 记录请求发送时间
                local_time_before = time.time() * 1000  # 转换为毫秒
                
                # 获取服务器时间
                server_time = await self.get_server_time()
                if server_time is None:
                    return False
                
                # 记录请求返回时间
                local_time_after = time.time() * 1000
                
                # 估算网络延迟并计算时钟偏差
                network_delay = (local_time_after - local_time_before) / 2
                local_time_estimated = local_time_before + network_delay
                
                # 计算偏差（服务器时间 - 本地时间）
                old_offset = self.server_time_offset
                self.server_time_offset = server_time - local_time_estimated
                self.last_time_sync = current_time
                
                offset_change = abs(self.server_time_offset - old_offset)
                
                if offset_change > 1000:  # 偏差变化超过1秒
                    self.logger.warning(
                        f"⚠️ 时钟偏差显著变化: {old_offset:.1f}ms -> {self.server_time_offset:.1f}ms"
                    )
                else:
                    self.logger.debug(
                        f"🕐 时钟同步完成: 偏差={self.server_time_offset:.1f}ms, 网络延迟={network_delay:.1f}ms"
                    )
                
                return True
                
            except Exception as e:
                self.logger.error(f"时钟同步失败: {e}")
                return False
    
    def get_synced_timestamp(self) -> str:
        """获取同步后的时间戳 - 用于API签名"""
        # 使用校准后的时间
        local_time_ms = time.time() * 1000
        synced_time_ms = local_time_ms + self.server_time_offset
        
        # 转换为ISO 8601格式
        synced_datetime = datetime.fromtimestamp(synced_time_ms / 1000, timezone.utc)
        timestamp = synced_datetime.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        
        return timestamp
    
    def _get_headers(self, method: str, request_path: str, body: str = '') -> Dict[str, str]:
        """获取API请求头 - 使用同步后的时间戳"""
        # 使用同步后的时间戳，防止时钟偏差导致签名失败
        timestamp = self.get_synced_timestamp()
        signature = self._generate_signature(timestamp, method, request_path, body)
        
        headers = {
            'OK-ACCESS-KEY': self.config.API_KEY,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.config.API_PASSWORD,
            'Content-Type': 'application/json'
        }
        
        if self.config.USE_SANDBOX:
            headers['x-simulated-trading'] = '1'
        
        return headers
    
    async def _connect_dual_channels(self):
        """连接双通道WebSocket"""
        try:
            self.logger.info("🚀 开始连接OKX双通道WebSocket")
            
            # 并发连接公共和私有频道
            public_task = asyncio.create_task(self._connect_public_channel(), name="connect_public")
            private_task = asyncio.create_task(self._connect_private_channel(), name="connect_private")
            
            # 等待两个连接完成
            public_result, private_result = await asyncio.gather(
                public_task, private_task, return_exceptions=True
            )
            
            # 检查连接结果
            if isinstance(public_result, Exception):
                self.logger.error(f"公共频道连接失败: {public_result}")
                raise public_result
            
            if isinstance(private_result, Exception):
                self.logger.error(f"私有频道连接失败: {private_result}")
                raise private_result
            
            self.logger.info("✅ 双通道WebSocket连接成功")
            
            # 启动消息处理任务
            await self._start_message_handlers()
            
        except Exception as e:
            self.logger.error(f"双通道连接失败: {e}")
            await self._handle_dual_channel_reconnect()
    
    async def _connect_public_channel(self):
        """连接公共频道"""
        try:
            self.logger.info("🔗 连接公共频道...")
            
            self.ws_public = await websockets.connect(
                self.ws_public_url,
                ping_interval=30,
                ping_timeout=10
            )
            
            self.is_public_connected = True
            self.reconnect_attempts['public'] = 0
            
            self.logger.info("✅ 公共频道连接成功")
            
        except Exception as e:
            self.logger.error(f"公共频道连接失败: {e}")
            raise
    
    async def _connect_private_channel(self):
        """连接私有频道并进行身份验证"""
        try:
            self.logger.info("🔐 连接私有频道...")
            
            self.ws_private = await websockets.connect(
                self.ws_private_url,
                ping_interval=30,
                ping_timeout=10
            )
            
            self.is_private_connected = True
            self.reconnect_attempts['private'] = 0
            
            self.logger.info("✅ 私有频道连接成功")
            
            # 进行身份验证
            await self._authenticate_private_channel()
            
        except Exception as e:
            self.logger.error(f"私有频道连接失败: {e}")
            raise
    
    async def _authenticate_private_channel(self):
        """私有频道身份验证"""
        try:
            self.logger.info("🔑 正在进行私有频道身份验证...")
            
            # 生成身份验证签名 - WebSocket API要求Unix时间戳（秒）
            timestamp = str(int(time.time()))
            method = 'GET'
            request_path = '/users/self/verify'
            
            # 按照OKX要求拼接签名字符串
            message = timestamp + method + request_path
            signature = base64.b64encode(
                hmac.new(
                    self.config.API_SECRET.encode('utf-8'),
                    message.encode('utf-8'),
                    hashlib.sha256
                ).digest()
            ).decode('utf-8')
            
            # 构建登录请求
            login_message = {
                "op": "login",
                "args": [{
                    "apiKey": self.config.API_KEY,
                    "passphrase": self.config.API_PASSWORD,
                    "timestamp": timestamp,
                    "sign": signature
                }]
            }
            
            # 发送登录请求
            await self.ws_private.send(json.dumps(login_message))
            
            # 等待登录响应
            response = await asyncio.wait_for(self.ws_private.recv(), timeout=10)
            response_data = json.loads(response)
            
            # 检查登录结果
            if response_data.get('event') == 'login' and response_data.get('code') == '0':
                self.is_private_authenticated = True
                self.logger.info("✅ 私有频道身份验证成功")
            else:
                error_msg = response_data.get('msg', '未知错误')
                raise Exception(f"身份验证失败: {error_msg}")
                
        except asyncio.TimeoutError:
            raise Exception("身份验证超时")
        except Exception as e:
            self.logger.error(f"身份验证失败: {e}")
            raise
    
    async def _handle_dual_channel_reconnect(self):
        """处理双通道重连"""
        # 检查重连次数
        total_attempts = max(self.reconnect_attempts.get('public', 0), self.reconnect_attempts.get('private', 0))
        if total_attempts >= self.max_reconnect_attempts:
            self.logger.error("达到最大重连次数，停止重连")
            return
        
        wait_time = min(5 * (total_attempts + 1), 60)
        self.logger.warning(f"🔄 双通道重连中... 第{total_attempts + 1}次，等待{wait_time}秒")
        await asyncio.sleep(wait_time)
        
        try:
            await self._connect_dual_channels()
        except Exception as e:
            self.logger.error(f"双通道重连失败: {e}")
            # 增加重连计数
            self.reconnect_attempts['public'] += 1
            self.reconnect_attempts['private'] += 1
            await self._handle_dual_channel_reconnect()
    
    async def _start_message_handlers(self):
        """启动消息处理任务"""
        try:
            # 启动公共频道消息处理
            if self.is_public_connected:
                self._public_message_task = asyncio.create_task(
                    self._handle_public_messages(),
                    name="public_message_handler"
                )
            
            # 启动私有频道消息处理
            if self.is_private_connected and self.is_private_authenticated:
                self._private_message_task = asyncio.create_task(
                    self._handle_private_messages(),
                    name="private_message_handler"
                )
            
            self.logger.info("📬 消息处理任务已启动")
            
        except Exception as e:
            self.logger.error(f"启动消息处理任务失败: {e}")
            raise
    
    async def subscribe_public_channel(self, channel: str, inst_ids: List[str]):
        """订阅公共频道"""
        if not self.is_public_connected:
            raise RuntimeError("公共频道未连接")
        
        channels = [{"channel": channel, "instId": inst_id} for inst_id in inst_ids]
        
        message = {
            "op": "subscribe",
            "args": channels
        }
        
        try:
            await self.ws_public.send(json.dumps(message))
            self.logger.info(f"✅ 已订阅公共频道 {channel}: {inst_ids}")
        except Exception as e:
            self.logger.error(f"订阅公共频道失败: {e}")
            raise
    
    async def subscribe_private_channel(self, channel: str, inst_type: str = None, inst_id: str = None):
        """订阅私有频道"""
        if not self.is_private_connected or not self.is_private_authenticated:
            raise RuntimeError("私有频道未连接或未认证")
        
        # 构建订阅参数
        args = [{"channel": channel}]
        if inst_type:
            args[0]["instType"] = inst_type
        if inst_id:
            args[0]["instId"] = inst_id
        
        message = {
            "op": "subscribe",
            "args": args
        }
        
        try:
            await self.ws_private.send(json.dumps(message))
            self.logger.info(f"✅ 已订阅私有频道 {channel}")
        except Exception as e:
            self.logger.error(f"订阅私有频道失败: {e}")
            raise
    
    async def subscribe_tickers(self, inst_ids: List[str]):
        """订阅价格行情（兼容性方法）"""
        await self.subscribe_public_channel("tickers", inst_ids)
    
    async def subscribe_order_books(self, inst_ids: List[str]):
        """订阅订单簿"""
        await self.subscribe_public_channel("books", inst_ids)
    
    async def subscribe_account_updates(self):
        """订阅账户更新"""
        await self.subscribe_private_channel("account")
    
    async def subscribe_order_updates(self, inst_type: str = "SWAP"):
        """订阅订单更新"""
        await self.subscribe_private_channel("orders", inst_type=inst_type)
    
    async def subscribe_position_updates(self, inst_type: str = "SWAP"):
        """订阅仓位更新"""
        await self.subscribe_private_channel("positions", inst_type=inst_type)
    
    async def subscribe_funding_rates(self, inst_ids: List[str]):
        """订阅资金费率（用于净利润计算）"""
        await self.subscribe_public_channel("funding-rate", inst_ids)
    
    async def subscribe_trades(self, inst_ids: List[str]):
        """订阅逐笔成交数据（用于微观结构分析）"""
        await self.subscribe_public_channel("trades", inst_ids)
    
    async def subscribe_order_books_5(self, inst_ids: List[str]):
        """订阅5档订单簿（专用于微观结构分析）"""
        await self.subscribe_public_channel("books5", inst_ids)
    
    async def subscribe_order_books_full(self, inst_ids: List[str]):
        """订阅完整订单簿（400档深度）"""
        await self.subscribe_public_channel("books-l2-tbt", inst_ids)
    
    async def _handle_public_messages(self):
        """处理公共频道消息"""
        try:
            async for message in self.ws_public:
                try:
                    data = json.loads(message)
                    
                    # 检查是否为错误响应
                    if 'error' in data or data.get('event') == 'error':
                        self.logger.error(f"公共频道错误: {data}")
                        continue
                    
                    # 分发消息到相应的事件处理器
                    await self._dispatch_public_message(data)
                    
                except json.JSONDecodeError as e:
                    self.logger.error(f"解析公共频道消息失败: {e}")
                except Exception as e:
                    self.logger.error(f"处理公共频道消息失败: {e}")
        
        except ConnectionClosed:
            self.logger.warning("公共频道连接关闭")
            self.is_public_connected = False
            await self._handle_public_channel_reconnect()
        except WebSocketException as e:
            self.logger.error(f"公共频道WebSocket异常: {e}")
            self.is_public_connected = False
            await self._handle_public_channel_reconnect()
    
    async def _handle_private_messages(self):
        """处理私有频道消息"""
        try:
            async for message in self.ws_private:
                try:
                    data = json.loads(message)
                    
                    # 检查是否为错误响应
                    if 'error' in data or data.get('event') == 'error':
                        self.logger.error(f"私有频道错误: {data}")
                        continue
                    
                    # 分发消息到相应的事件处理器
                    await self._dispatch_private_message(data)
                    
                except json.JSONDecodeError as e:
                    self.logger.error(f"解析私有频道消息失败: {e}")
                except Exception as e:
                    self.logger.error(f"处理私有频道消息失败: {e}")
        
        except ConnectionClosed:
            self.logger.warning("私有频道连接关闭")
            self.is_private_connected = False
            self.is_private_authenticated = False
            await self._handle_private_channel_reconnect()
        except WebSocketException as e:
            self.logger.error(f"私有频道WebSocket异常: {e}")
            self.is_private_connected = False
            self.is_private_authenticated = False
            await self._handle_private_channel_reconnect()
    
    async def _handle_public_channel_reconnect(self):
        """公共频道重连"""
        if self.reconnect_attempts['public'] >= self.max_reconnect_attempts:
            self.logger.error("公共频道达到最大重连次数")
            return
        
        self.reconnect_attempts['public'] += 1
        wait_time = min(5 * self.reconnect_attempts['public'], 60)
        
        self.logger.warning(f"🔄 公共频道重连中... 第{self.reconnect_attempts['public']}次")
        await asyncio.sleep(wait_time)
        
        try:
            await self._connect_public_channel()
            # 重新启动消息处理
            self._public_message_task = asyncio.create_task(self._handle_public_messages())
        except Exception as e:
            self.logger.error(f"公共频道重连失败: {e}")
            await self._handle_public_channel_reconnect()
    
    async def _handle_private_channel_reconnect(self):
        """私有频道重连"""
        if self.reconnect_attempts['private'] >= self.max_reconnect_attempts:
            self.logger.error("私有频道达到最大重连次数")
            return
        
        self.reconnect_attempts['private'] += 1
        wait_time = min(5 * self.reconnect_attempts['private'], 60)
        
        self.logger.warning(f"🔄 私有频道重连中... 第{self.reconnect_attempts['private']}次")
        await asyncio.sleep(wait_time)
        
        try:
            await self._connect_private_channel()
            # 重新启动消息处理
            self._private_message_task = asyncio.create_task(self._handle_private_messages())
        except Exception as e:
            self.logger.error(f"私有频道重连失败: {e}")
            await self._handle_private_channel_reconnect()
    
    async def place_order(self, inst_id: str, trade_mode: str, side: str, 
                         ord_type: str, sz: str, px: Optional[str] = None,
                         **kwargs) -> Dict[str, Any]:
        """异步下单 - 集成速率限制器"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        # 应用速率限制 - 下单API
        if not await self.order_rate_limiter.wait_for_tokens(1, timeout=10.0):
            raise RuntimeError("下单速率限制超时 - API请求过于频繁")
        
        request_path = "/api/v5/trade/order"
        
        order_data = {
            "instId": inst_id,
            "tdMode": trade_mode,
            "side": side,
            "ordType": ord_type,
            "sz": sz
        }
        
        if px:
            order_data["px"] = px
        
        # 为期货合约自动添加posSide参数
        if "SWAP" in inst_id or "FUTURES" in inst_id:
            # 对于net模式，使用net；对于long/short模式，根据side确定
            if trade_mode == "cross" or trade_mode == "isolated":
                order_data["posSide"] = "net"  # 单向持仓模式
            else:
                # 双向持仓模式（如果使用）
                order_data["posSide"] = "long" if side == "buy" else "short"
        
        # 添加其他参数
        order_data.update(kwargs)
        
        # 调试日志：显示发送的订单数据
        self.logger.debug(f"📤 [{inst_id}] 发送订单数据: {json.dumps(order_data, indent=2)}")
        
        body = json.dumps(order_data)
        headers = self._get_headers("POST", request_path, body)
        
        try:
            start_time = time.time()
            
            async with self.http_session.post(
                f"{self.base_url}{request_path}",
                data=body,
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                latency = (time.time() - start_time) * 1000
                self.logger.info(f"下单完成，延迟: {latency:.2f}ms")
                
                if latency > self.config.MAX_ORDER_LATENCY_MS:
                    self.logger.warning(f"下单延迟过高: {latency:.2f}ms")
                
                return result
                
        except Exception as e:
            self.logger.error(f"下单失败: {e}")
            raise
    
    async def _execute_smart_order(self, inst_id: str, side: str, size: str, trade_mode: str) -> Dict[str, Any]:
        """执行单条腿的三阶段智能订单"""
        smart_config = self.config.ORDER_EXECUTION["SMART_EXECUTION"]
        execution_result = {
            "inst_id": inst_id,
            "side": side,
            "requested_size": float(size),
            "filled_size": 0.0,
            "avg_price": 0.0,
            "execution_stages": [],
            "success": False,
            "order_ids": []
        }
        
        try:
            # 阶段一：Post-Only Maker尝试
            self.logger.info(f"🔄 [{inst_id}] 阶段一: Post-Only Maker尝试")
            maker_result = await self._stage_one_maker(inst_id, side, size, trade_mode, smart_config)
            execution_result["execution_stages"].append(maker_result)
            
            if maker_result["success"]:
                execution_result.update({
                    "filled_size": maker_result["filled_size"],
                    "avg_price": maker_result["avg_price"],
                    "success": True,
                    "order_ids": [maker_result["order_id"]]
                })
                self.logger.info(f"✅ [{inst_id}] Maker成交成功: {maker_result['filled_size']}")
                return execution_result
            
            # 阶段二：IOC Taker执行
            self.logger.info(f"🔄 [{inst_id}] 阶段二: IOC Taker执行")
            taker_result = await self._stage_two_taker(inst_id, side, size, trade_mode, smart_config)
            execution_result["execution_stages"].append(taker_result)
            
            if taker_result["success"]:
                execution_result.update({
                    "filled_size": taker_result["filled_size"],
                    "avg_price": taker_result["avg_price"],
                    "success": True,
                    "order_ids": [taker_result["order_id"]]
                })
                self.logger.info(f"✅ [{inst_id}] Taker成交成功: {taker_result['filled_size']}")
                return execution_result
            
            # 如果智能执行失败，根据配置决定是否回退到市价单
            if self.config.ORDER_EXECUTION.get("FALLBACK_TO_MARKET", True):
                self.logger.warning(f"⚠️  [{inst_id}] 智能执行失败，回退到市价单")
                self.logger.info(f"🎯 [{inst_id}] 市价单参数: {side} {size} @ 市价")
                
                market_result = await self.place_order(
                    inst_id=inst_id,
                    trade_mode=trade_mode,
                    side=side,
                    ord_type="market",
                    sz=size
                )
                
                if market_result.get('code') == '0':
                    order_data = market_result['data'][0]
                    order_id = order_data['ordId']
                    
                    # 等待订单成交并获取成交信息
                    filled_info = await self._wait_for_order_fill(order_id, inst_id, timeout=10)
                    
                    if filled_info['success']:
                        execution_result.update({
                            "filled_size": filled_info['filled_size'],
                            "avg_price": filled_info['avg_price'],
                            "success": True,
                            "order_ids": [order_id]
                        })
                        execution_result["execution_stages"].append({
                            "stage": "fallback_market",
                            "success": True,
                            "order_id": order_id,
                            "filled_size": filled_info['filled_size'],
                            "avg_price": filled_info['avg_price']
                        })
                        self.logger.info(f"✅ [{inst_id}] 市价单成交成功: {filled_info['filled_size']} @ {filled_info['avg_price']}")
                    else:
                        self.logger.error(f"❌ [{inst_id}] 市价单成交失败或超时")
                        execution_result["success"] = False
                else:
                    # 市价单下单也失败了
                    error_msg = market_result.get('msg', 'Unknown error')
                    error_code = market_result.get('code', 'Unknown')
                    self.logger.error(f"❌ [{inst_id}] 市价单下单失败: 错误码={error_code}, 消息={error_msg}")
                    self.logger.error(f"完整API响应: {json.dumps(market_result, indent=2)}")
                    execution_result["success"] = False
                    execution_result["error"] = f"市价单失败: {error_code}: {error_msg}"
                    
            return execution_result
            
        except Exception as e:
            self.logger.error(f"智能订单执行失败 [{inst_id}]: {e}")
            execution_result["error"] = str(e)
            return execution_result
    
    async def _wait_for_order_fill(self, order_id: str, inst_id: str, timeout: int = 10) -> Dict[str, Any]:
        """等待订单成交并获取成交信息 - 混合确认机制（WebSocket优先 + REST备用）"""
        start_time = time.time()
        websocket_timeout = min(timeout * 0.7, 5)  # WebSocket等待时间不超过总时间的70%或5秒
        
        # 第一阶段：优先使用WebSocket实时数据
        try:
            # 检查是否已有WebSocket数据
            async with self._order_lock:
                if order_id in self.order_results:
                    cached_result = self.order_results[order_id]
                    state = cached_result.get('state', '')
                    
                    if state == 'filled':
                        self.logger.debug(f"⚡ 从WebSocket缓存获取订单状态: {order_id} -> {state}")
                        return {
                            'success': True,
                            'filled_size': float(cached_result.get('fillSz', 0)),
                            'avg_price': float(cached_result.get('avgPx', 0)),
                            'state': state
                        }
                    elif state == 'canceled':
                        self.logger.warning(f"⚡ 从WebSocket缓存发现订单已取消: {order_id}")
                        return {
                            'success': False,
                            'error': 'canceled',
                            'filled_size': 0,
                            'avg_price': 0
                        }
            
            # 如果没有缓存数据，尝试WebSocket等待
            self.logger.debug(f"⚡ [{inst_id}] WebSocket等待订单确认: {order_id} (超时: {websocket_timeout}s)")
            websocket_result = await self._wait_for_order_completion(order_id, int(websocket_timeout * 1000))
            
            if websocket_result:
                state = websocket_result.get('state', '')
                if state == 'filled':
                    self.logger.debug(f"✅ WebSocket确认订单成交: {order_id}")
                    return {
                        'success': True,
                        'filled_size': float(websocket_result.get('fillSz', 0)),
                        'avg_price': float(websocket_result.get('avgPx', 0)),
                        'state': state
                    }
                elif state == 'canceled':
                    self.logger.warning(f"❌ WebSocket确认订单已取消: {order_id}")
                    return {
                        'success': False,
                        'error': 'canceled',
                        'filled_size': 0,
                        'avg_price': 0
                    }
        
        except Exception as e:
            self.logger.warning(f"WebSocket等待失败: {order_id} - {e}")
        
        # 第二阶段：WebSocket超时或失败，回退到REST轮询
        remaining_time = timeout - (time.time() - start_time)
        if remaining_time <= 0:
            self.logger.warning(f"等待订单成交超时: {order_id}")
            return {
                'success': False,
                'error': 'timeout',
                'filled_size': 0,
                'avg_price': 0
            }
        
        self.logger.debug(f"📞 [{inst_id}] WebSocket超时，回退到REST轮询: {order_id} (剩余: {remaining_time:.1f}s)")
        check_interval = 0.5
        
        while time.time() - start_time < timeout:
            try:
                # 查询订单状态
                order_info = await self._get_order_info(order_id, inst_id)
                
                if order_info['success']:
                    order_data = order_info['data']
                    state = order_data.get('state', '')
                    
                    if state == 'filled':
                        self.logger.debug(f"✅ REST确认订单成交: {order_id}")
                        return {
                            'success': True,
                            'filled_size': float(order_data.get('fillSz', 0)),
                            'avg_price': float(order_data.get('avgPx', 0)),
                            'state': state
                        }
                    elif state in ['canceled', 'live']:
                        if state == 'canceled':
                            self.logger.warning(f"❌ REST确认订单已取消: {order_id}")
                            return {
                                'success': False,
                                'error': 'canceled',
                                'filled_size': 0,
                                'avg_price': 0
                            }
                        continue
                    elif state == 'partially_filled':
                        continue
                
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"REST查询订单状态失败 {order_id}: {e}")
                await asyncio.sleep(check_interval)
        
        # 最终超时
        self.logger.warning(f"⏰ 订单成交确认最终超时: {order_id}")
        return {
            'success': False,
            'error': 'timeout',
            'filled_size': 0,
            'avg_price': 0
        }
    
    async def _get_order_info(self, order_id: str, inst_id: str) -> Dict[str, Any]:
        """获取订单信息"""
        try:
            request_path = f"/api/v5/trade/order?instId={inst_id}&ordId={order_id}"
            headers = self._get_headers("GET", request_path, "")
            
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    return {
                        'success': True,
                        'data': result['data'][0]
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('msg', 'Unknown error')
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _stage_one_maker(self, inst_id: str, side: str, size: str, trade_mode: str, config: Dict) -> Dict:
        """阶段一：Post-Only Maker尝试（WebSocket事件驱动，零延迟）"""
        try:
            # 获取最新盘口价格
            best_prices = await self.get_best_prices(inst_id)
            if not best_prices:
                return {"stage": "maker", "success": False, "error": "无法获取盘口价格"}
            
            # 计算Maker价格（买单在买一价，卖单在卖一价）
            if side == "buy":
                maker_price = best_prices['bid'] + config.get('MAKER_PRICE_OFFSET', 0.0)
            else:
                maker_price = best_prices['ask'] - config.get('MAKER_PRICE_OFFSET', 0.0)
            
            # 调整价格精度
            tick_size = self.get_tick_size(inst_id)
            maker_price = round(maker_price / tick_size) * tick_size
            
            self.logger.debug(f"🎯 [{inst_id}] Maker订单准备: {side} {size} @ {maker_price:.6f} (买一:{best_prices['bid']:.6f}, 卖一:{best_prices['ask']:.6f})")
            
            # 下达Post-Only订单
            result = await self.place_order(
                inst_id=inst_id,
                trade_mode=trade_mode,
                side=side,
                ord_type="post_only",
                sz=size,
                px=str(maker_price)
            )
            
            if result.get('code') != '0':
                error_msg = result.get('msg', 'Unknown error')
                error_code = result.get('code', 'Unknown')
                self.logger.error(f"❌ [{inst_id}] Maker订单下单失败: 错误码={error_code}, 消息={error_msg}")
                self.logger.error(f"完整API响应: {json.dumps(result, indent=2)}")
                self.logger.error(f"订单参数: inst_id={inst_id}, side={side}, size={size}, price={maker_price}")
                return {"stage": "maker", "success": False, "error": f"{error_code}: {error_msg}"}
            
            order_id = result['data'][0]['ordId']
            
            # 混合等待模式：WebSocket + REST查询备用
            timeout_ms = config.get('POST_ONLY_TIMEOUT_MS', 2000)
            self.logger.debug(f"⚡ [{inst_id}] Maker订单 {order_id} 等待确认...")
            
            # 先尝试WebSocket等待
            order_status = await self._wait_for_order_completion(order_id, min(timeout_ms, 1000))
            
            # 如果WebSocket等待失败，使用REST查询
            if not order_status:
                self.logger.debug(f"📞 [{inst_id}] WebSocket等待超时，使用REST查询")
                order_status = await self.get_order_status(inst_id, order_id)
            
            if not order_status:
                # 彻底超时，取消订单
                self.logger.debug(f"⏰ [{inst_id}] Maker订单超时，取消订单 {order_id}")
                await self.cancel_order(inst_id, order_id)
                return {"stage": "maker", "success": False, "order_id": order_id, "error": "订单状态查询超时"}
            
            # 解析订单状态
            state = order_status.get('state')
            filled_size = float(order_status.get('fillSz', 0))
            avg_price = float(order_status.get('avgPx', 0))
            
            if state == 'filled':
                self.logger.debug(f"✅ [{inst_id}] Maker订单完全成交: {filled_size} @ {avg_price}")
                return {
                    "stage": "maker",
                    "success": True,
                    "order_id": order_id,
                    "filled_size": filled_size,
                    "avg_price": avg_price,
                    "maker_price": maker_price
                }
            else:
                # 取消未完成的订单
                self.logger.debug(f"⚠️  [{inst_id}] Maker订单未完全成交，取消: {state}")
                await self.cancel_order(inst_id, order_id)
                return {
                    "stage": "maker",
                    "success": False,
                    "order_id": order_id,
                    "partial_fill": filled_size,
                    "reason": f"未完全成交，状态: {state}"
                }
                
        except Exception as e:
            return {"stage": "maker", "success": False, "error": str(e)}
    
    async def _stage_two_taker(self, inst_id: str, side: str, size: str, trade_mode: str, config: Dict) -> Dict:
        """阶段二：IOC Taker执行（WebSocket事件驱动，零延迟）"""
        try:
            # 重新获取最新盘口价格
            best_prices = await self.get_best_prices(inst_id)
            if not best_prices:
                return {"stage": "taker", "success": False, "error": "无法获取盘口价格"}
            
            # 计算Taker价格（买单在卖一价，卖单在买一价，加上偏移）
            if side == "buy":
                taker_price = best_prices['ask'] + config.get('TAKER_PRICE_OFFSET', 0.0001)
            else:
                taker_price = best_prices['bid'] - config.get('TAKER_PRICE_OFFSET', 0.0001)
            
            # 调整价格精度
            tick_size = self.get_tick_size(inst_id)
            taker_price = round(taker_price / tick_size) * tick_size
            
            self.logger.debug(f"⚡ [{inst_id}] Taker订单准备: {side} {size} @ {taker_price:.6f} (买一:{best_prices['bid']:.6f}, 卖一:{best_prices['ask']:.6f})")
            
            # 下达IOC订单
            result = await self.place_order(
                inst_id=inst_id,
                trade_mode=trade_mode,
                side=side,
                ord_type="ioc",
                sz=size,
                px=str(taker_price)
            )
            
            if result.get('code') != '0':
                error_msg = result.get('msg', 'Unknown error')
                error_code = result.get('code', 'Unknown')
                self.logger.error(f"❌ [{inst_id}] Taker订单下单失败: 错误码={error_code}, 消息={error_msg}")
                self.logger.error(f"完整API响应: {json.dumps(result, indent=2)}")
                self.logger.error(f"订单参数: inst_id={inst_id}, side={side}, size={size}, price={taker_price}")
                return {"stage": "taker", "success": False, "error": f"{error_code}: {error_msg}"}
            
            order_id = result['data'][0]['ordId']
            
            # 混合等待模式：WebSocket + REST查询备用
            timeout_ms = config.get('IOC_TIMEOUT_MS', 1000)
            self.logger.debug(f"⚡ [{inst_id}] Taker订单 {order_id} 等待确认...")
            
            # 先尝试WebSocket等待
            order_status = await self._wait_for_order_completion(order_id, min(timeout_ms, 500))
            
            # 如果WebSocket等待失败，使用REST查询
            if not order_status:
                self.logger.debug(f"📞 [{inst_id}] WebSocket等待超时，使用REST查询")
                order_status = await self.get_order_status(inst_id, order_id)
            
            if not order_status:
                # IOC订单应该立即执行，如果还查不到则认为失败
                self.logger.debug(f"⏰ [{inst_id}] Taker订单状态查询失败")
                return {"stage": "taker", "success": False, "order_id": order_id, "error": "订单状态查询超时"}
            
            # 解析订单状态
            filled_size = float(order_status.get('fillSz', 0))
            avg_price = float(order_status.get('avgPx', 0))
            state = order_status.get('state')
            
            if filled_size > 0:
                self.logger.debug(f"✅ [{inst_id}] Taker订单成交: {filled_size} @ {avg_price}")
                return {
                    "stage": "taker",
                    "success": True,
                    "order_id": order_id,
                    "filled_size": filled_size,
                    "avg_price": avg_price,
                    "taker_price": taker_price,
                    "final_state": state
                }
            
            self.logger.debug(f"❌ [{inst_id}] Taker订单无成交: {state}")
            return {"stage": "taker", "success": False, "order_id": order_id, "reason": "IOC订单无成交"}
            
        except Exception as e:
            return {"stage": "taker", "success": False, "error": str(e)}
    
    async def _handle_order_completion(self, data: Dict[str, Any]):
        """处理订单完成事件（WebSocket驱动的状态跟踪）"""
        try:
            order_data = data.get("data", [])
            
            for order_info in order_data:
                order_id = order_info.get("ordId")
                if not order_id:
                    continue
                
                # 检查是否是我们跟踪的订单
                async with self._order_lock:
                    if order_id in self.pending_orders:
                        # 更新订单结果
                        self.order_results[order_id] = {
                            "ordId": order_id,
                            "instId": order_info.get("instId"),
                            "state": order_info.get("state"),
                            "side": order_info.get("side"),
                            "fillSz": order_info.get("fillSz", "0"),
                            "avgPx": order_info.get("avgPx", "0"),
                            "sz": order_info.get("sz", "0"),
                            "px": order_info.get("px", "0"),
                            "ordType": order_info.get("ordType"),
                            "updateTime": order_info.get("uTime")
                        }
                        
                        # 设置事件，通知等待的任务
                        self.pending_orders[order_id].set()
                        
                        self.logger.debug(f"📋 订单状态更新: {order_id} -> {order_info.get('state')}")
                        
        except Exception as e:
            self.logger.error(f"处理订单完成事件失败: {e}")
    
    async def _wait_for_order_completion(self, order_id: str, timeout_ms: int = 5000) -> Optional[Dict[str, Any]]:
        """等待订单完成（基于WebSocket事件，非REST查询）"""
        try:
            # 创建事件对象用于等待
            async with self._order_lock:
                completion_event = asyncio.Event()
                self.pending_orders[order_id] = completion_event
            
            # 等待订单完成事件，带超时
            try:
                await asyncio.wait_for(completion_event.wait(), timeout=timeout_ms / 1000.0)
                
                # 获取订单结果
                async with self._order_lock:
                    result = self.order_results.get(order_id)
                    # 清理跟踪
                    self.pending_orders.pop(order_id, None)
                    self.order_results.pop(order_id, None)
                
                return result
                
            except asyncio.TimeoutError:
                # 超时清理
                async with self._order_lock:
                    self.pending_orders.pop(order_id, None)
                    self.order_results.pop(order_id, None)
                
                self.logger.warning(f"⏰ 订单 {order_id} 等待超时 ({timeout_ms}ms)")
                return None
                
        except Exception as e:
            self.logger.error(f"等待订单完成失败: {e}")
            return None
    
    async def place_arbitrage_orders(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """执行原子性套利订单对 - 确保双边同时成功或同时失败"""
        self.logger.info("🚀 开始原子性套利订单执行")
        
        # 检查是否启用智能执行
        if not self.config.ORDER_EXECUTION.get("USE_SMART_EXECUTION", True):
            # 回退到传统执行
            return await self._execute_traditional_orders(spot_params, futures_params)
        
        try:
            # 阶段1: 尝试同时下达两个Post-Only订单
            self.logger.info("📋 阶段1: 同时下达Post-Only订单")
            spot_result, futures_result = await self._execute_atomic_post_only(spot_params, futures_params)
            
            if spot_result.get('success') and futures_result.get('success'):
                self.logger.info("✅ Post-Only订单双边成交成功")
                return spot_result, futures_result
            
            # 阶段2: Post-Only失败，尝试IOC订单
            self.logger.info("📋 阶段2: 尝试IOC订单配对")
            spot_result, futures_result = await self._execute_atomic_ioc(spot_params, futures_params)
            
            if spot_result.get('success') and futures_result.get('success'):
                self.logger.info("✅ IOC订单双边成交成功")
                return spot_result, futures_result
            
            # 阶段3: 最后尝试市价单（风险最高，但最容易成交）
            self.logger.info("📋 阶段3: 尝试市价单配对")
            spot_result, futures_result = await self._execute_atomic_market(spot_params, futures_params)
            
            if spot_result.get('success') and futures_result.get('success'):
                self.logger.info("✅ 市价单双边成交成功")
                return spot_result, futures_result
            
            # 所有阶段都失败
            self.logger.error("❌ 所有执行阶段都失败，套利订单未执行")
            return (
                {"success": False, "error": "所有执行策略失败", "filled_size": 0, "avg_price": 0},
                {"success": False, "error": "所有执行策略失败", "filled_size": 0, "avg_price": 0}
            )
            
        except Exception as e:
            self.logger.error(f"原子性套利订单执行异常: {e}")
            raise
    
    async def _execute_traditional_orders(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """传统订单执行（备用）"""
        self.logger.info("使用传统订单执行模式")
        
        # 创建并发任务
        spot_task = asyncio.create_task(
            self.place_order(**spot_params),
            name="spot_order"
        )
        
        futures_task = asyncio.create_task(
            self.place_order(**futures_params),
            name="futures_order"
        )
        
        try:
            # 并发等待两个订单完成
            spot_result, futures_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 转换为统一格式
            spot_success = not isinstance(spot_result, Exception) and spot_result.get('code') == '0'
            futures_success = not isinstance(futures_result, Exception) and futures_result.get('code') == '0'
            
            if spot_success and futures_success:
                self.logger.info("传统套利订单对执行成功")
            else:
                self.logger.error(f"传统套利订单执行失败 - 现货: {spot_success}, 期货: {futures_success}")
            
            return spot_result, futures_result
            
        except Exception as e:
            self.logger.error(f"传统套利订单执行异常: {e}")
            raise
    
    async def get_positions(self, inst_type: str = "SWAP") -> Dict[str, Any]:
        """异步获取持仓"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = f"/api/v5/account/positions?instType={inst_type}"
        headers = self._get_headers("GET", request_path)
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                return result
                
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            raise
    
    async def get_position_risk(self, inst_id: str) -> Optional[Dict[str, Any]]:
        """获取特定仓位的风险信息"""
        try:
            positions_data = await self.get_positions()

            if positions_data.get('code') == '0':
                for position in positions_data.get('data', []):
                    if position.get('instId') == inst_id:
                        return position

            return None

        except Exception as e:
            self.logger.error(f"获取仓位风险信息失败: {e}")
            return None

    async def get_funding_rate(self, inst_id: str) -> Optional[Dict[str, Any]]:
        """获取永续合约当前资金费率"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")

        request_path = f"/api/v5/public/funding-rate?instId={inst_id}"

        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()

                if result.get('code') == '0' and result.get('data'):
                    return result['data'][0]
                else:
                    self.logger.warning(f"获取{inst_id}资金费率失败: {result.get('msg', 'Unknown error')}")
                    return None

        except Exception as e:
            self.logger.error(f"请求{inst_id}资金费率失败: {e}")
            return None

    async def get_ticker(self, inst_id: str) -> Optional[Dict[str, Any]]:
        """获取单个产品行情信息"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")

        request_path = f"/api/v5/market/ticker?instId={inst_id}"

        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()

                if result.get('code') == '0' and result.get('data'):
                    return result['data'][0]
                else:
                    self.logger.warning(f"获取{inst_id}行情信息失败: {result.get('msg', 'Unknown error')}")
                    return None

        except Exception as e:
            self.logger.error(f"请求{inst_id}行情信息失败: {e}")
            return None
    
    async def cancel_order(self, inst_id: str, ord_id: str) -> Dict[str, Any]:
        """异步撤单"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = "/api/v5/trade/cancel-order"
        
        cancel_data = {
            "instId": inst_id,
            "ordId": ord_id
        }
        
        body = json.dumps(cancel_data)
        headers = self._get_headers("POST", request_path, body)
        
        try:
            async with self.http_session.post(
                f"{self.base_url}{request_path}",
                data=body,
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                return result
                
        except Exception as e:
            self.logger.error(f"撤单失败: {e}")
            raise
    
    async def get_order_status(self, inst_id: str, ord_id: str) -> Optional[Dict[str, Any]]:
        """查询订单状态"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = f"/api/v5/trade/order?instId={inst_id}&ordId={ord_id}"
        headers = self._get_headers("GET", request_path)
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    return result['data'][0]
                return None
                
        except Exception as e:
            self.logger.error(f"查询订单状态失败: {e}")
            return None
    
    async def get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = "/api/v5/account/balance"
        headers = self._get_headers("GET", request_path)
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                return result
                
        except Exception as e:
            self.logger.error(f"获取账户余额失败: {e}")
            raise
    
    async def get_order_book(self, inst_id: str, sz: int = 5) -> Optional[Dict[str, Any]]:
        """获取实时盘口深度数据 - 集成速率限制器"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        # 应用速率限制 - 查询API
        if not await self.query_rate_limiter.wait_for_tokens(1, timeout=5.0):
            self.logger.warning("查询速率限制超时，跳过本次请求")
            return None
        
        request_path = f"/api/v5/market/books?instId={inst_id}&sz={sz}"
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    return result['data'][0]
                else:
                    self.logger.warning(f"获取{inst_id}盘口数据失败: {result.get('msg', 'Unknown error')}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"请求{inst_id}盘口数据失败: {e}")
            return None
            
    async def _get_instrument_precision(self, inst_id: str) -> Dict[str, Any]:
        """获取交易对精度规格信息，带缓存机制"""
        # 检查缓存
        if hasattr(self, '_precision_cache') and inst_id in self._precision_cache:
            return self._precision_cache[inst_id]
        
        if not hasattr(self, '_precision_cache'):
            self._precision_cache = {}
        
        if not self.http_session:
            self.logger.warning("HTTP会话未初始化，使用默认精度")
            return {'minSz': 1e-8, 'tickSz': 1e-8}
        
        # 应用速率限制
        if not await self.query_rate_limiter.wait_for_tokens(1, timeout=5.0):
            self.logger.warning("查询速率限制超时，使用默认精度")
            return {'minSz': 1e-8, 'tickSz': 1e-8}
        
        request_path = f"/api/v5/public/instruments?instType=SPOT&instId={inst_id}"
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    instrument_data = result['data'][0]
                    precision_info = {
                        'minSz': float(instrument_data.get('minSz', 1e-8)),
                        'tickSz': float(instrument_data.get('tickSz', 1e-8)),
                        'lotSz': float(instrument_data.get('lotSz', 1e-8)),
                        'ctVal': float(instrument_data.get('ctVal', 1)),
                        'instId': inst_id
                    }
                    
                    # 缓存结果（12小时有效）
                    self._precision_cache[inst_id] = precision_info
                    self.logger.debug(f"📏 缓存交易对精度: {inst_id} -> minSz={precision_info['minSz']}")
                    
                    return precision_info
                else:
                    self.logger.warning(f"获取{inst_id}精度规格失败: {result.get('msg', 'unknown error')}")
                    return {'minSz': 1e-8, 'tickSz': 1e-8}
                    
        except Exception as e:
            self.logger.error(f"查询{inst_id}精度规格异常: {e}")
            return {'minSz': 1e-8, 'tickSz': 1e-8}
    
    async def get_best_prices(self, inst_id: str) -> Optional[Dict[str, float]]:
        """获取最佳买卖价格 - WebSocket数据优先，REST备用"""
        # 第一优先：检查WebSocket缓存数据
        async with self.orderbook_lock:
            cached_data = self.orderbook_cache.get(inst_id)
            
            if cached_data:
                # 检查数据新鲜度（1秒内的数据认为是新鲜的）
                current_time = int(time.time() * 1000)
                data_age = current_time - int(cached_data['timestamp'])
                
                if data_age < 1000:  # 1秒内
                    self.logger.debug(f"⚡ 使用WebSocket缓存盘口 {inst_id}: {data_age}ms延迟")
                    best_bid = cached_data['best_bid']
                    best_ask = cached_data['best_ask']
                    
                    return {
                        'bid': best_bid,
                        'ask': best_ask,
                        'spread': best_ask - best_bid,
                        'mid': (best_bid + best_ask) / 2
                    }
                else:
                    self.logger.debug(f"📡 WebSocket数据过期 {inst_id}: {data_age}ms，回退到REST")
        
        # 第二优先：确保WebSocket订阅
        if inst_id not in self.orderbook_subscriptions:
            try:
                await self.subscribe_order_books([inst_id])
                self.orderbook_subscriptions.add(inst_id)
                self.logger.info(f"🔔 自动订阅盘口数据: {inst_id}")
            except Exception as e:
                self.logger.warning(f"自动订阅盘口失败: {e}")
        
        # 第三优先：REST API回退
        self.logger.debug(f"📞 使用REST获取盘口 {inst_id}")
        order_book = await self.get_order_book(inst_id, sz=1)
        if not order_book:
            return None
        
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                return None
            
            best_bid = float(bids[0][0])  # 最佳买价
            best_ask = float(asks[0][0])  # 最佳卖价
            
            return {
                'bid': best_bid,
                'ask': best_ask,
                'spread': best_ask - best_bid,
                'mid': (best_bid + best_ask) / 2
            }
            
        except (IndexError, ValueError, TypeError) as e:
            self.logger.error(f"解析{inst_id}盘口价格失败: {e}")
            return None
    
    async def ensure_orderbook_subscription(self, inst_ids: List[str]):
        """确保WebSocket盘口数据订阅 - 用于批量预订阅"""
        missing_subscriptions = [inst_id for inst_id in inst_ids if inst_id not in self.orderbook_subscriptions]
        
        if missing_subscriptions:
            try:
                await self.subscribe_order_books(missing_subscriptions)
                self.orderbook_subscriptions.update(missing_subscriptions)
                self.logger.info(f"🔔 批量订阅盘口数据: {missing_subscriptions}")
            except Exception as e:
                self.logger.error(f"批量订阅盘口失败: {e}")
    
    async def atomic_order_with_live_pricing(self, inst_id: str, trade_mode: str, side: str, 
                                           ord_type: str, sz: str, pricing_strategy: str = "maker",
                                           price_offset: float = 0.0, **kwargs) -> Dict[str, Any]:
        """原子化下单 - 基于WebSocket实时数据的零延迟定价"""
        try:
            # 确保已订阅盘口数据
            if inst_id not in self.orderbook_subscriptions:
                await self.ensure_orderbook_subscription([inst_id])
                # 等待短暂时间让WebSocket数据到达
                await asyncio.sleep(0.1)
            
            # 原子化操作：获取当前盘口快照并立即计算价格
            async with self.orderbook_lock:
                orderbook_snapshot = self.orderbook_cache.get(inst_id)
                
                if not orderbook_snapshot:
                    # 没有WebSocket数据，回退到REST
                    self.logger.warning(f"⚠️ [{inst_id}] 无WebSocket盘口数据，回退到REST")
                    return await self._fallback_to_rest_order(inst_id, trade_mode, side, ord_type, sz, **kwargs)
                
                # 检查数据新鲜度
                current_time = int(time.time() * 1000)
                data_age = current_time - int(orderbook_snapshot['timestamp'])
                
                if data_age > 2000:  # 数据超过2秒
                    self.logger.warning(f"⚠️ [{inst_id}] WebSocket数据过期({data_age}ms)，回退到REST")
                    return await self._fallback_to_rest_order(inst_id, trade_mode, side, ord_type, sz, **kwargs)
                
                # 原子计算订单价格
                best_bid = orderbook_snapshot['best_bid']
                best_ask = orderbook_snapshot['best_ask']
                
                # 根据策略计算价格
                if pricing_strategy == "maker":
                    # Maker策略：买单挂买一价，卖单挂卖一价
                    if side == "buy":
                        price = best_bid + price_offset
                    else:  # sell
                        price = best_ask + price_offset
                        
                elif pricing_strategy == "taker":
                    # Taker策略：买单吃卖一价，卖单吃买一价
                    if side == "buy":
                        price = best_ask + price_offset
                    else:  # sell
                        price = best_bid + price_offset
                        
                elif pricing_strategy == "mid":
                    # 中间价策略
                    mid_price = (best_bid + best_ask) / 2
                    price = mid_price + price_offset
                    
                else:
                    raise ValueError(f"不支持的定价策略: {pricing_strategy}")
                
                # 记录原子化操作的时间窗口
                snapshot_age = data_age
                
            # 立即下单 - 最小化竞态条件窗口
            order_result = await self.place_order(
                inst_id=inst_id,
                trade_mode=trade_mode,
                side=side,
                ord_type=ord_type,
                sz=sz,
                px=str(round(price, 8)),  # 确保价格精度
                **kwargs
            )
            
            # 记录原子化操作统计
            if order_result.get('code') == '0':
                self.logger.debug(
                    f"⚡ 原子化下单成功 [{inst_id}]: "
                    f"策略={pricing_strategy}, 价格={price:.8f}, "
                    f"快照延迟={snapshot_age}ms"
                )
                order_result['atomic_pricing'] = {
                    'strategy': pricing_strategy,
                    'calculated_price': price,
                    'snapshot_age_ms': snapshot_age,
                    'best_bid': best_bid,
                    'best_ask': best_ask
                }
            else:
                self.logger.warning(
                    f"❌ 原子化下单失败 [{inst_id}]: {order_result.get('msg', 'Unknown error')}"
                )
            
            return order_result
            
        except Exception as e:
            self.logger.error(f"原子化下单异常 [{inst_id}]: {e}")
            # 异常时回退到传统方法
            return await self._fallback_to_rest_order(inst_id, trade_mode, side, ord_type, sz, **kwargs)
    
    async def _fallback_to_rest_order(self, inst_id: str, trade_mode: str, side: str, 
                                     ord_type: str, sz: str, **kwargs) -> Dict[str, Any]:
        """回退到传统REST下单方法"""
        self.logger.debug(f"📞 [{inst_id}] 使用传统REST下单方法")
        
        # 如果没有指定价格，获取当前盘口价格
        if 'px' not in kwargs and ord_type in ['limit', 'post_only']:
            best_prices = await self.get_best_prices(inst_id)
            if best_prices:
                # 使用简单的默认定价策略
                if side == "buy":
                    price = best_prices['bid']
                else:
                    price = best_prices['ask']
                kwargs['px'] = str(round(price, 8))
        
        return await self.place_order(inst_id, trade_mode, side, ord_type, sz, **kwargs)
    
    async def get_orderbook_cache_status(self) -> Dict[str, Any]:
        """获取盘口缓存状态"""
        async with self.orderbook_lock:
            current_time = int(time.time() * 1000)
            cache_status = {}
            
            for inst_id, data in self.orderbook_cache.items():
                age = current_time - int(data['timestamp'])
                cache_status[inst_id] = {
                    'age_ms': age,
                    'fresh': age < 1000,
                    'best_bid': data['best_bid'],
                    'best_ask': data['best_ask']
                }
            
            return {
                'subscriptions': list(self.orderbook_subscriptions),
                'cached_instruments': cache_status,
                'total_subscriptions': len(self.orderbook_subscriptions),
                'fresh_data_count': sum(1 for status in cache_status.values() if status['fresh'])
            }
    
    async def initialize_instrument_specs(self) -> bool:
        """初始化交易对规格信息"""
        try:
            self.logger.info("开始初始化交易对规格信息...")
            
            # 获取现货交易对规格
            spot_specs = await self._fetch_instrument_specs("SPOT")
            futures_specs = await self._fetch_instrument_specs("SWAP")
            
            # 缓存现货规格
            if spot_specs:
                for spec in spot_specs:
                    inst_id = spec.get('instId')
                    if inst_id == self.config.SPOT_ID:
                        self.instrument_specs[inst_id] = {
                            'instType': spec.get('instType'),
                            'instId': inst_id,
                            'baseCcy': spec.get('baseCcy'),
                            'quoteCcy': spec.get('quoteCcy'),
                            'settleCcy': spec.get('settleCcy'),
                            'ctVal': spec.get('ctVal', '1'),  # 合约面值
                            'ctMult': spec.get('ctMult', '1'),  # 合约乘数
                            'tickSz': spec.get('tickSz'),   # 价格精度
                            'lotSz': spec.get('lotSz'),     # 最小交易数量
                            'minSz': spec.get('minSz'),     # 最小下单数量
                            'maxMktSz': spec.get('maxMktSz'),  # 市价单最大数量
                            'maxLmtSz': spec.get('maxLmtSz'),  # 限价单最大数量
                            'maxTwapSz': spec.get('maxTwapSz'), # TWAP最大数量
                            'state': spec.get('state'),      # 交易状态
                            'listTime': spec.get('listTime'), # 上市时间
                        }
                        self.logger.info(f"现货规格已缓存: {inst_id}")
                        break
            
            # 缓存期货规格
            if futures_specs:
                for spec in futures_specs:
                    inst_id = spec.get('instId')
                    if inst_id == self.config.FUTURES_ID:
                        self.instrument_specs[inst_id] = {
                            'instType': spec.get('instType'),
                            'instId': inst_id,
                            'uly': spec.get('uly'),         # 标的指数
                            'baseCcy': spec.get('baseCcy'),
                            'quoteCcy': spec.get('quoteCcy'),
                            'settleCcy': spec.get('settleCcy'),
                            'ctVal': spec.get('ctVal', '1'),  # 合约面值
                            'ctMult': spec.get('ctMult', '1'),  # 合约乘数
                            'ctValCcy': spec.get('ctValCcy'), # 合约面值计价货币
                            'tickSz': spec.get('tickSz'),   # 价格精度
                            'lotSz': spec.get('lotSz'),     # 最小交易数量
                            'minSz': spec.get('minSz'),     # 最小下单数量
                            'maxMktSz': spec.get('maxMktSz'),  # 市价单最大数量
                            'maxLmtSz': spec.get('maxLmtSz'),  # 限价单最大数量
                            'maxTwapSz': spec.get('maxTwapSz'), # TWAP最大数量
                            'state': spec.get('state'),      # 交易状态
                            'listTime': spec.get('listTime'), # 上市时间
                            'expTime': spec.get('expTime'),   # 到期时间
                            'lever': spec.get('lever'),       # 最大杠杆倍数
                        }
                        self.logger.info(f"期货规格已缓存: {inst_id}")
                        break
            
            # 验证必要的规格是否已获取
            if self.config.SPOT_ID not in self.instrument_specs:
                self.logger.error(f"未能获取现货交易对规格: {self.config.SPOT_ID}")
                return False
            
            if self.config.FUTURES_ID not in self.instrument_specs:
                self.logger.error(f"未能获取期货交易对规格: {self.config.FUTURES_ID}")
                return False
            
            self.logger.info(f"交易对规格初始化完成: {list(self.instrument_specs.keys())}")
            
            # 记录关键规格信息
            for inst_id, spec in self.instrument_specs.items():
                self.logger.info(
                    f"📋 {inst_id} 规格: "
                    f"合约面值={spec.get('ctVal', 'N/A')}, "
                    f"最小交易量={spec.get('lotSz', 'N/A')}, "
                    f"价格精度={spec.get('tickSz', 'N/A')}, "
                    f"最小下单量={spec.get('minSz', 'N/A')}"
                )
            
            return True
            
        except Exception as e:
            self.logger.error(f"初始化交易对规格失败: {e}")
            return False
    
    async def _fetch_instrument_specs(self, inst_type: str) -> Optional[List[Dict[str, Any]]]:
        """获取指定类型的交易对规格"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = f"/api/v5/public/instruments?instType={inst_type}"
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0':
                    return result.get('data', [])
                else:
                    self.logger.error(f"获取{inst_type}规格失败: {result.get('msg')}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"请求{inst_type}规格失败: {e}")
            return None
    
    def get_instrument_spec(self, inst_id: str) -> Optional[Dict[str, Any]]:
        """安全获取交易对规格信息"""
        return self.instrument_specs.get(inst_id)
    
    def get_contract_value(self, inst_id: str) -> float:
        """获取合约面值"""
        spec = self.get_instrument_spec(inst_id)
        if spec:
            try:
                return float(spec.get('ctVal', '1'))
            except (ValueError, TypeError):
                return 1.0
        return 1.0
    
    def get_lot_size(self, inst_id: str) -> float:
        """获取最小交易单位"""
        spec = self.get_instrument_spec(inst_id)
        if spec:
            try:
                return float(spec.get('lotSz', '1'))
            except (ValueError, TypeError):
                return 1.0
        return 1.0
    
    def get_tick_size(self, inst_id: str) -> float:
        """获取价格最小变动单位"""
        spec = self.get_instrument_spec(inst_id)
        if spec:
            try:
                return float(spec.get('tickSz', '0.00001'))
            except (ValueError, TypeError):
                return 0.00001
        return 0.00001
    
    def get_min_size(self, inst_id: str) -> float:
        """获取最小下单数量"""
        spec = self.get_instrument_spec(inst_id)
        if spec:
            try:
                return float(spec.get('minSz', '1'))
            except (ValueError, TypeError):
                return 1.0
        return 1.0
    
    async def place_market_hedge_order(self, inst_id: str, side: str, size: float) -> Dict[str, Any]:
        """执行市价对冲订单"""
        try:
            self.logger.info(f"执行对冲订单: {inst_id} {side} {size}")
            
            # 获取交易模式
            trade_mode = "cash" if "USDT" in inst_id and "SWAP" not in inst_id else "cross"
            
            # 确保订单大小符合规格
            min_size = self.get_min_size(inst_id)
            lot_size = self.get_lot_size(inst_id)
            
            # 调整订单大小
            adjusted_size = max(abs(size), min_size)
            adjusted_size = (adjusted_size // lot_size) * lot_size
            
            if adjusted_size < min_size:
                adjusted_size = min_size
            
            result = await self.place_order(
                inst_id=inst_id,
                trade_mode=trade_mode,
                side=side,
                ord_type="market",
                sz=str(adjusted_size)
            )
            
            self.logger.info(f"对冲订单结果: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"执行对冲订单失败: {e}")
            raise
    
    def register_event_callback(self, event_type: str, callback: Callable):
        """注册事件回调函数"""
        if event_type not in self.event_callbacks:
            self.event_callbacks[event_type] = []
        
        if callback not in self.event_callbacks[event_type]:
            self.event_callbacks[event_type].append(callback)
            self.logger.info(f"✅ 已注册{event_type}事件回调")
    
    def unregister_event_callback(self, event_type: str, callback: Callable):
        """取消注册事件回调函数"""
        if event_type in self.event_callbacks and callback in self.event_callbacks[event_type]:
            self.event_callbacks[event_type].remove(callback)
            self.logger.info(f"❌ 已取消注册{event_type}事件回调")
    
    async def _dispatch_public_message(self, data: Dict[str, Any]):
        """分发公共频道消息"""
        try:
            # 检查消息类型
            if 'arg' in data and 'data' in data:
                channel = data['arg'].get('channel')
                
                # 根据频道类型分发消息
                if channel == 'tickers':
                    await self._trigger_callbacks('ticker', data)
                elif channel in ['books', 'books5', 'books-l2-tbt']:
                    await self._handle_orderbook_update(data)
                    await self._trigger_callbacks('books', data)
                elif channel == 'trades':
                    await self._trigger_callbacks('trades', data)
                elif channel == 'funding-rate':
                    await self._trigger_callbacks('funding-rate', data)
                else:
                    self.logger.debug(f"未处理的公共频道消息: {channel}")
            
            elif data.get('event') == 'subscribe':
                self.logger.info(f"订阅确认: {data.get('arg', {})}")
            else:
                self.logger.debug(f"未处理的公共消息: {data}")
                
        except Exception as e:
            self.logger.error(f"分发公共频道消息失败: {e}")
    
    async def _dispatch_private_message(self, data: Dict[str, Any]):
        """分发私有频道消息"""
        try:
            # 检查消息类型
            if 'arg' in data and 'data' in data:
                channel = data['arg'].get('channel')
                
                # 根据频道类型分发消息
                if channel == 'account':
                    await self._trigger_callbacks('account', data)
                elif channel == 'orders':
                    await self._trigger_callbacks('orders', data)
                elif channel == 'positions':
                    await self._trigger_callbacks('positions', data)
                elif channel == 'balance':
                    await self._trigger_callbacks('balance', data)
                else:
                    self.logger.debug(f"未处理的私有频道消息: {channel}")
            
            elif data.get('event') == 'subscribe':
                self.logger.info(f"私有频道订阅确认: {data.get('arg', {})}")
            elif data.get('event') == 'login':
                self.logger.debug("私有频道登录响应已在认证方法中处理")
            else:
                self.logger.debug(f"未处理的私有消息: {data}")
                
        except Exception as e:
            self.logger.error(f"分发私有频道消息失败: {e}")
    
    async def _handle_orderbook_update(self, data: Dict[str, Any]):
        """处理WebSocket盘口数据更新并缓存"""
        try:
            inst_id = data['arg'].get('instId')
            if not inst_id:
                return
            
            book_data = data.get('data', [])
            if not book_data:
                return
            
            # 获取第一个盘口数据（通常只有一个）
            orderbook = book_data[0]
            
            # 提取买卖盘数据
            bids = orderbook.get('bids', [])
            asks = orderbook.get('asks', [])
            timestamp = orderbook.get('ts', str(int(time.time() * 1000)))
            
            if bids and asks:
                # 缓存最新盘口数据
                async with self.orderbook_lock:
                    self.orderbook_cache[inst_id] = {
                        'bids': bids,
                        'asks': asks, 
                        'timestamp': timestamp,
                        'best_bid': float(bids[0][0]) if bids[0] else 0,
                        'best_ask': float(asks[0][0]) if asks[0] else 0,
                        'bid_size': float(bids[0][1]) if bids[0] else 0,
                        'ask_size': float(asks[0][1]) if asks[0] else 0
                    }
                
                self.logger.debug(f"📈 更新盘口缓存 {inst_id}: bid={bids[0][0]}, ask={asks[0][0]}")
                
        except Exception as e:
            self.logger.error(f"处理盘口数据更新失败: {e}")

    async def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]):
        """触发事件回调"""
        if event_type in self.event_callbacks:
            callbacks = self.event_callbacks[event_type]
            if callbacks:
                # 并发执行所有回调
                tasks = []
                for callback in callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            tasks.append(asyncio.create_task(callback(data)))
                        else:
                            # 对于同步函数，在线程池中执行
                            loop = asyncio.get_event_loop()
                            tasks.append(loop.run_in_executor(None, callback, data))
                    except Exception as e:
                        self.logger.error(f"创建{event_type}回调任务失败: {e}")
                
                if tasks:
                    # 等待所有回调完成，但不传播异常
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            self.logger.error(f"{event_type}回调执行失败: {result}")
    
    async def health_check(self) -> bool:
        """双通道健康检查"""
        try:
            # 检查公共频道
            public_healthy = (
                self.ws_public and 
                not getattr(self.ws_public, 'closed', True) and 
                self.is_public_connected
            )
            
            # 检查私有频道
            private_healthy = (
                self.ws_private and 
                not getattr(self.ws_private, 'closed', True) and 
                self.is_private_connected and 
                self.is_private_authenticated
            )
            
            # 检查HTTP会话
            http_healthy = self.http_session and not self.http_session.closed
            
            overall_health = public_healthy and private_healthy and http_healthy
            
            self.logger.debug(
                f"健康状态: 公共={public_healthy}, "
                f"私有={private_healthy}, HTTP={http_healthy}, "
                f"总体={overall_health}"
            )
            
            return overall_health
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False
    
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态详情"""
        return {
            "public_channel": {
                "connected": self.is_public_connected,
                "websocket_open": self.ws_public and not getattr(self.ws_public, 'closed', True),
                "reconnect_attempts": self.reconnect_attempts.get('public', 0)
            },
            "private_channel": {
                "connected": self.is_private_connected,
                "authenticated": self.is_private_authenticated,
                "websocket_open": self.ws_private and not getattr(self.ws_private, 'closed', True),
                "reconnect_attempts": self.reconnect_attempts.get('private', 0)
            },
            "http_session": {
                "active": self.http_session and not self.http_session.closed
            },
            "event_callbacks": {
                event_type: len(callbacks) 
                for event_type, callbacks in self.event_callbacks.items()
            }
        }
    
    async def initialize_instrument_specs(self) -> bool:
        """初始化所有配置的交易对的合约规格"""
        if self.instrument_specs_initialized:
            return True
            
        try:
            self.logger.info("🔍 正在获取交易对合约规格...")
            
            # 从配置中获取所有启用的交易对
            from config import get_enabled_trading_pairs
            enabled_pairs = get_enabled_trading_pairs()
            
            # 获取现货和期货的所有symbol
            spot_symbols = []
            futures_symbols = []
            for pair in enabled_pairs:
                spot_symbols.append(pair["spot_id"])
                futures_symbols.append(pair["futures_id"])
            
            # 查询现货规格
            spot_specs = await self._get_instrument_specs("SPOT", spot_symbols)
            # 查询期货规格  
            futures_specs = await self._get_instrument_specs("SWAP", futures_symbols)
            
            # 合并规格数据
            self.instrument_specs.update(spot_specs)
            self.instrument_specs.update(futures_specs)
            
            # 记录获取到的规格信息
            self.logger.info(f"✅ 成功获取 {len(self.instrument_specs)} 个交易对的合约规格")
            for inst_id, spec in self.instrument_specs.items():
                self.logger.debug(f"📋 [{inst_id}] 规格: 合约面值={spec.get('ctVal', 'N/A')}, "
                                f"最小变动={spec.get('tickSz', 'N/A')}, "
                                f"最小数量={spec.get('lotSz', 'N/A')}")
            
            self.instrument_specs_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 获取合约规格失败: {e}")
            return False
    
    async def _get_instrument_specs(self, inst_type: str, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """查询指定类型和交易对的合约规格"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
            
        specs = {}
        
        try:
            # OKX API: /api/v5/public/instruments
            request_path = f"/api/v5/public/instruments?instType={inst_type}"
            
            # 公共API不需要签名
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
            if result.get('code') == '0':
                instruments = result.get('data', [])
                
                # 过滤出配置中的交易对
                for instrument in instruments:
                    inst_id = instrument.get('instId')
                    if inst_id in symbols:
                        specs[inst_id] = {
                            'instId': inst_id,
                            'instType': inst_type,
                            'ctVal': instrument.get('ctVal', '1'),  # 合约面值
                            'ctMult': instrument.get('ctMult', '1'),  # 合约乘数
                            'tickSz': instrument.get('tickSz', '0.01'),  # 最小变动价位
                            'lotSz': instrument.get('lotSz', '1'),  # 最小交易数量
                            'minSz': instrument.get('minSz', '1'),  # 最小订单数量
                            'maxMktSz': instrument.get('maxMktSz', '999999'),  # 市价单最大数量
                            'maxLmtSz': instrument.get('maxLmtSz', '999999'),  # 限价单最大数量
                            'baseCcy': instrument.get('baseCcy', ''),  # 基础货币
                            'quoteCcy': instrument.get('quoteCcy', ''),  # 计价货币
                            'settleCcy': instrument.get('settleCcy', ''),  # 结算货币
                        }
                        
                self.logger.info(f"🔍 获取 {inst_type} 规格: {len(specs)}/{len(symbols)} 个交易对")
            else:
                self.logger.error(f"❌ 查询 {inst_type} 规格失败: {result.get('msg', 'Unknown error')}")
                
        except Exception as e:
            self.logger.error(f"❌ 获取 {inst_type} 合约规格异常: {e}")
            
        return specs
    
    def get_contract_value(self, inst_id: str) -> float:
        """获取合约面值（用于期货数量计算）"""
        if not self.instrument_specs_initialized:
            self.logger.warning(f"⚠️ 合约规格未初始化，使用默认值")
            return 1.0
            
        spec = self.instrument_specs.get(inst_id)
        if spec:
            return float(spec.get('ctVal', '1'))
        else:
            self.logger.warning(f"⚠️ 未找到 {inst_id} 的合约规格，使用默认值")
            return 1.0
    
    def get_tick_size(self, inst_id: str) -> float:
        """获取最小变动价位"""
        if not self.instrument_specs_initialized:
            self.logger.warning(f"⚠️ 合约规格未初始化，使用默认值")
            return 0.01
            
        spec = self.instrument_specs.get(inst_id)
        if spec:
            return float(spec.get('tickSz', '0.01'))
        else:
            self.logger.warning(f"⚠️ 未找到 {inst_id} 的价格精度，使用默认值")
            return 0.01
    
    def get_lot_size(self, inst_id: str) -> float:
        """获取最小交易数量"""
        if not self.instrument_specs_initialized:
            self.logger.warning(f"⚠️ 合约规格未初始化，使用默认值")
            return 1.0
            
        spec = self.instrument_specs.get(inst_id)
        if spec:
            return float(spec.get('lotSz', '1'))
        else:
            self.logger.warning(f"⚠️ 未找到 {inst_id} 的数量精度，使用默认值")
            return 1.0
    
    def calculate_futures_quantity(self, spot_quantity: float, spot_price: float, futures_inst_id: str) -> float:
        """
        根据现货数量和价格计算对应的期货合约数量
        
        Args:
            spot_quantity: 现货数量
            spot_price: 现货价格
            futures_inst_id: 期货合约ID
            
        Returns:
            期货合约数量
        """
        # 计算现货名义价值
        spot_notional = spot_quantity * spot_price
        
        # 获取期货合约面值
        contract_value = self.get_contract_value(futures_inst_id)
        
        # 计算期货合约数量
        # OKX期货合约: 1张合约 = ctVal * 币种数量
        # 例如: ETH-USDT-SWAP, ctVal=0.1, 表示1张合约 = 0.1 ETH
        # 所以: 合约数量 = 现货数量 / ctVal
        futures_quantity = spot_quantity / contract_value
        
        # 调整到最小交易单位
        lot_size = self.get_lot_size(futures_inst_id)
        futures_quantity = round(futures_quantity / lot_size) * lot_size
        
        self.logger.debug(f"💱 数量转换: 现货{spot_quantity} * {spot_price} = {spot_notional:.2f} USDT")
        self.logger.debug(f"💱 合约面值: {contract_value}, 期货数量: {futures_quantity}")
        
        return futures_quantity
    
    async def _execute_atomic_post_only(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """原子性执行Post-Only订单对"""
        try:
            # 获取盘口价格
            spot_prices = await self.get_best_prices(spot_params["inst_id"])
            futures_prices = await self.get_best_prices(futures_params["inst_id"])
            
            if not spot_prices or not futures_prices:
                return (
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0}
                )
            
            # 计算Post-Only价格（确保不会立即成交）
            spot_price = spot_prices['bid'] if spot_params["side"] == "sell" else spot_prices['ask']
            futures_price = futures_prices['bid'] if futures_params["side"] == "sell" else futures_prices['ask']
            
            # 价格精度调整
            spot_price = round(spot_price / self.get_tick_size(spot_params["inst_id"])) * self.get_tick_size(spot_params["inst_id"])
            futures_price = round(futures_price / self.get_tick_size(futures_params["inst_id"])) * self.get_tick_size(futures_params["inst_id"])
            
            # 并发下单
            spot_task = asyncio.create_task(self.place_order(
                inst_id=spot_params["inst_id"],
                trade_mode=spot_params["trade_mode"],
                side=spot_params["side"],
                ord_type="post_only",
                sz=spot_params["sz"],
                px=str(spot_price)
            ))
            
            futures_task = asyncio.create_task(self.place_order(
                inst_id=futures_params["inst_id"],
                trade_mode=futures_params["trade_mode"],
                side=futures_params["side"],
                ord_type="post_only",
                sz=futures_params["sz"],
                px=str(futures_price)
            ))
            
            # 等待两个订单结果
            spot_order_result, futures_order_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 检查订单提交是否成功
            spot_order_success = (not isinstance(spot_order_result, Exception) and 
                                spot_order_result.get('code') == '0')
            futures_order_success = (not isinstance(futures_order_result, Exception) and 
                                   futures_order_result.get('code') == '0')
            
            if not spot_order_success or not futures_order_success:
                # 如果有一边下单失败，取消另一边的订单
                await self._cancel_failed_atomic_orders(spot_order_result, futures_order_result, 
                                                       spot_params, futures_params)
                return (
                    {"success": False, "error": "Post-Only下单失败", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "Post-Only下单失败", "filled_size": 0, "avg_price": 0}
                )
            
            # 提取订单ID
            spot_order_id = spot_order_result['data'][0]['ordId']
            futures_order_id = futures_order_result['data'][0]['ordId']
            
            # 等待订单成交（短时间）
            post_only_timeout = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["POST_ONLY_TIMEOUT_MS"]
            spot_fill_task = asyncio.create_task(
                self._wait_for_order_fill(spot_order_id, spot_params["inst_id"], post_only_timeout // 1000)
            )
            futures_fill_task = asyncio.create_task(
                self._wait_for_order_fill(futures_order_id, futures_params["inst_id"], post_only_timeout // 1000)
            )
            
            spot_fill_result, futures_fill_result = await asyncio.gather(
                spot_fill_task, futures_fill_task
            )
            
            # 检查是否双边都成交
            both_filled = spot_fill_result['success'] and futures_fill_result['success']
            
            if both_filled:
                return (
                    {
                        "success": True,
                        "filled_size": spot_fill_result['filled_size'],
                        "avg_price": spot_fill_result['avg_price'],
                        "order_ids": [spot_order_id]
                    },
                    {
                        "success": True,
                        "filled_size": futures_fill_result['filled_size'],
                        "avg_price": futures_fill_result['avg_price'],
                        "order_ids": [futures_order_id]
                    }
                )
            else:
                # 取消未成交的订单
                if not spot_fill_result['success']:
                    await self.cancel_order(spot_params["inst_id"], spot_order_id)
                if not futures_fill_result['success']:
                    await self.cancel_order(futures_params["inst_id"], futures_order_id)
                
                return (
                    {"success": False, "error": "Post-Only超时未成交", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "Post-Only超时未成交", "filled_size": 0, "avg_price": 0}
                )
                
        except Exception as e:
            self.logger.error(f"Post-Only原子执行失败: {e}")
            return (
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0},
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0}
            )
    
    async def _execute_atomic_ioc(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """原子性执行IOC订单对"""
        try:
            # 获取盘口价格
            spot_prices = await self.get_best_prices(spot_params["inst_id"])
            futures_prices = await self.get_best_prices(futures_params["inst_id"])
            
            if not spot_prices or not futures_prices:
                return (
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0}
                )
            
            # 计算IOC价格（略微穿越盘口确保成交）
            taker_offset = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["TAKER_PRICE_OFFSET"]
            
            if spot_params["side"] == "buy":
                spot_price = spot_prices['ask'] + taker_offset
            else:
                spot_price = spot_prices['bid'] - taker_offset
                
            if futures_params["side"] == "buy":
                futures_price = futures_prices['ask'] + taker_offset
            else:
                futures_price = futures_prices['bid'] - taker_offset
            
            # 价格精度调整
            spot_price = round(spot_price / self.get_tick_size(spot_params["inst_id"])) * self.get_tick_size(spot_params["inst_id"])
            futures_price = round(futures_price / self.get_tick_size(futures_params["inst_id"])) * self.get_tick_size(futures_params["inst_id"])
            
            # 并发执行IOC订单
            spot_task = asyncio.create_task(self.place_order(
                inst_id=spot_params["inst_id"],
                trade_mode=spot_params["trade_mode"],
                side=spot_params["side"],
                ord_type="ioc",
                sz=spot_params["sz"],
                px=str(spot_price)
            ))
            
            futures_task = asyncio.create_task(self.place_order(
                inst_id=futures_params["inst_id"],
                trade_mode=futures_params["trade_mode"],
                side=futures_params["side"],
                ord_type="ioc",
                sz=futures_params["sz"],
                px=str(futures_price)
            ))
            
            # 等待订单结果
            spot_result, futures_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 检查订单结果并验证成交
            return await self._validate_atomic_results(spot_result, futures_result, spot_params, futures_params)
            
        except Exception as e:
            self.logger.error(f"IOC原子执行失败: {e}")
            return (
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0},
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0}
            )
    
    async def _execute_atomic_market(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """原子性执行市价单对（最后手段）"""
        try:
            # 并发执行市价单
            spot_task = asyncio.create_task(self.place_order(
                inst_id=spot_params["inst_id"],
                trade_mode=spot_params["trade_mode"],
                side=spot_params["side"],
                ord_type="market",
                sz=spot_params["sz"]
            ))
            
            futures_task = asyncio.create_task(self.place_order(
                inst_id=futures_params["inst_id"],
                trade_mode=futures_params["trade_mode"],
                side=futures_params["side"],
                ord_type="market",
                sz=futures_params["sz"]
            ))
            
            # 等待订单结果
            spot_result, futures_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 检查订单结果并验证成交
            return await self._validate_atomic_results(spot_result, futures_result, spot_params, futures_params)
            
        except Exception as e:
            self.logger.error(f"市价单原子执行失败: {e}")
            return (
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0},
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0}
            )
    
    async def _validate_atomic_results(self, spot_result, futures_result, spot_params: Dict, futures_params: Dict) -> tuple:
        """验证原子性执行结果"""
        # 检查是否有异常
        if isinstance(spot_result, Exception) or isinstance(futures_result, Exception):
            return (
                {"success": False, "error": "订单执行异常", "filled_size": 0, "avg_price": 0},
                {"success": False, "error": "订单执行异常", "filled_size": 0, "avg_price": 0}
            )
        
        # 检查订单是否成功提交
        spot_success = spot_result.get('code') == '0'
        futures_success = futures_result.get('code') == '0'
        
        if not spot_success or not futures_success:
            # 记录失败原因
            spot_error = spot_result.get('msg', 'Unknown error') if not spot_success else None
            futures_error = futures_result.get('msg', 'Unknown error') if not futures_success else None
            
            self.logger.warning(f"原子性订单失败 - 现货: {spot_error}, 期货: {futures_error}")
            
            return (
                {"success": False, "error": spot_error or "配对失败", "filled_size": 0, "avg_price": 0},
                {"success": False, "error": futures_error or "配对失败", "filled_size": 0, "avg_price": 0}
            )
        
        # 提取订单ID并等待成交确认
        spot_order_id = spot_result['data'][0]['ordId']
        futures_order_id = futures_result['data'][0]['ordId']
        
        # 等待双边成交确认
        ioc_timeout = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["IOC_TIMEOUT_MS"] // 1000
        
        spot_fill_task = asyncio.create_task(
            self._wait_for_order_fill(spot_order_id, spot_params["inst_id"], ioc_timeout)
        )
        futures_fill_task = asyncio.create_task(
            self._wait_for_order_fill(futures_order_id, futures_params["inst_id"], ioc_timeout)
        )
        
        spot_fill_result, futures_fill_result = await asyncio.gather(
            spot_fill_task, futures_fill_task
        )
        
        # 严格验证双边成交和头寸匹配
        if spot_fill_result['success'] and futures_fill_result['success']:
            spot_filled = spot_fill_result['filled_size']
            futures_filled = futures_fill_result['filled_size']
            
            # 获取交易对精度规格进行精确匹配验证
            spot_precision = await self._get_instrument_precision(spot_params["inst_id"])
            futures_precision = await self._get_instrument_precision(futures_params["inst_id"])
            
            # 使用动态精度容差，基于交易对最小单位
            min_size_tolerance = max(spot_precision.get('minSz', 1e-8), futures_precision.get('minSz', 1e-8))
            tolerance = max(min_size_tolerance, 1e-8)  # 至少保持基础精度
            
            volume_diff = abs(spot_filled - futures_filled)
            max_volume = max(spot_filled, futures_filled)
            mismatch_ratio = volume_diff / max_volume if max_volume > 0 else 0
            
            self.logger.debug(f"🔍 严格双边成交量验证: 现货={spot_filled:.8f}, 期货={futures_filled:.8f}")
            self.logger.debug(f"📏 精度规格: 现货minSz={spot_precision.get('minSz')}, 期货minSz={futures_precision.get('minSz')}")
            self.logger.debug(f"📊 差异分析: 绝对差异={volume_diff:.8f}, 相对比例={mismatch_ratio:.8f}, 容差={tolerance:.8f}")
            
            # 如果成交量完全匹配，直接返回成功
            if mismatch_ratio <= tolerance:
                self.logger.info(f"✅ 双边成交量完全匹配: {spot_filled}")
                return (
                    {
                        "success": True,
                        "filled_size": spot_filled,
                        "avg_price": spot_fill_result['avg_price'],
                        "order_ids": [spot_order_id]
                    },
                    {
                        "success": True,
                        "filled_size": futures_filled,
                        "avg_price": futures_fill_result['avg_price'],
                        "order_ids": [futures_order_id]
                    }
                )
            
            # 成交量不匹配，需要对冲差额
            else:
                self.logger.warning(f"⚠️ 双边成交量不匹配! 现货: {spot_filled}, 期货: {futures_filled}, 差异: {volume_diff}")
                
                # 确定哪一边成交更多，需要对冲的方向和数量
                if spot_filled > futures_filled:
                    # 现货成交多了，需要卖出多余的现货头寸
                    hedge_direction = "sell_spot"
                    hedge_amount = volume_diff
                    matched_size = futures_filled
                else:
                    # 期货成交多了，需要平仓多余的期货头寸
                    hedge_direction = "close_futures"
                    hedge_amount = volume_diff
                    matched_size = spot_filled
                
                # 尝试对冲差额 - 增强精度和重试逻辑
                hedge_result = await self._hedge_position_mismatch(
                    hedge_direction, hedge_amount, spot_params, futures_params,
                    spot_precision, futures_precision
                )
                
                if hedge_result['success']:
                    self.logger.info(f"✅ 头寸差额对冲成功: {hedge_direction} {hedge_amount}")
                    # 对冲成功，返回匹配的成交量
                    return (
                        {
                            "success": True,
                            "filled_size": matched_size,
                            "avg_price": spot_fill_result['avg_price'],
                            "order_ids": [spot_order_id],
                            "hedge_info": hedge_result
                        },
                        {
                            "success": True,
                            "filled_size": matched_size,
                            "avg_price": futures_fill_result['avg_price'],
                            "order_ids": [futures_order_id],
                            "hedge_info": hedge_result
                        }
                    )
                else:
                    # 对冲失败，保留风险敞口但记录详细信息
                    self.logger.error(f"❌ 头寸差额对冲失败: {hedge_result.get('error', 'Unknown error')}")
                    self.logger.error(f"🚨 风险警告: 存在 {volume_diff} 的净头寸敞口!")
                    
                    return (
                        {
                            "success": True,  # 技术上成交了，但有风险
                            "filled_size": spot_filled,
                            "avg_price": spot_fill_result['avg_price'],
                            "order_ids": [spot_order_id],
                            "risk_warning": f"未对冲敞口: {volume_diff}",
                            "hedge_failed": hedge_result
                        },
                        {
                            "success": True,
                            "filled_size": futures_filled,
                            "avg_price": futures_fill_result['avg_price'],
                            "order_ids": [futures_order_id],
                            "risk_warning": f"未对冲敞口: {volume_diff}",
                            "hedge_failed": hedge_result
                        }
                    )
        else:
            # 如果有一边未成交，执行增强的错误回滚机制
            self.logger.warning("⚠️ 原子性成交失败，执行增强错误回滚")
            
            # 记录详细的失败信息用于风险分析
            failure_details = {
                'spot_success': spot_fill_result['success'],
                'futures_success': futures_fill_result['success'],
                'spot_filled': spot_fill_result.get('filled_size', 0),
                'futures_filled': futures_fill_result.get('filled_size', 0),
                'spot_error': spot_fill_result.get('error', 'unknown'),
                'futures_error': futures_fill_result.get('error', 'unknown')
            }
            
            self.logger.error(f"🚨 原子性失败详情: {failure_details}")
            
            # 尝试回滚部分成交的订单
            rollback_result = await self._enhanced_atomic_rollback(
                spot_fill_result, futures_fill_result, 
                spot_order_id, futures_order_id,
                spot_params, futures_params
            )
            
            if rollback_result['success']:
                self.logger.info(f"✅ 错误回滚成功: {rollback_result.get('actions', [])}")
            else:
                self.logger.error(f"❌ 错误回滚失败: {rollback_result.get('error', 'unknown')}")
            
            return (
                {
                    "success": False, 
                    "error": "原子性成交失败", 
                    "filled_size": failure_details['spot_filled'], 
                    "avg_price": spot_fill_result.get('avg_price', 0),
                    "rollback_info": rollback_result
                },
                {
                    "success": False, 
                    "error": "原子性成交失败", 
                    "filled_size": failure_details['futures_filled'], 
                    "avg_price": futures_fill_result.get('avg_price', 0),
                    "rollback_info": rollback_result
                }
            )
    
    async def _hedge_position_mismatch(self, hedge_direction: str, hedge_amount: float, 
                                     spot_params: Dict, futures_params: Dict,
                                     spot_precision: Dict = None, futures_precision: Dict = None) -> Dict[str, Any]:
        """对冲头寸不匹配的差额 - 增强精度处理和重试机制"""
        try:
            # 获取精度信息（如果没有提供）
            if spot_precision is None:
                spot_precision = await self._get_instrument_precision(spot_params["inst_id"])
            if futures_precision is None:
                futures_precision = await self._get_instrument_precision(futures_params["inst_id"])
            
            self.logger.info(f"🔄 开始对冲头寸差额: {hedge_direction} {hedge_amount:.8f}")
            self.logger.debug(f"📏 对冲精度控制: 现货minSz={spot_precision.get('minSz')}, 期货minSz={futures_precision.get('minSz')}")
            
            if hedge_direction == "sell_spot":
                # 现货成交多了，需要市价卖出多余的现货
                # 按交易对精度调整对冲数量
                min_sz = spot_precision.get('minSz', 1e-8)
                adjusted_amount = max(hedge_amount, min_sz)
                
                # 确保数量符合交易对规格 - 修复精度判断逻辑
                if hedge_amount < min_sz:
                    self.logger.warning(f"对冲数量{hedge_amount}小于最小交易单位{min_sz}，跳过对冲")
                    return {
                        'success': False,
                        'error': f"对冲数量太小: {hedge_amount} < {min_sz}",
                        'skipped': True
                    }
                
                hedge_params = {
                    "instId": spot_params["inst_id"],
                    "tdMode": "cash",  # 现货模式
                    "side": "sell",
                    "ordType": "market",
                    "sz": f"{adjusted_amount:.8f}".rstrip('0').rstrip('.')
                }
                
                self.logger.debug(f"📤 现货对冲参数: {hedge_params}")
                
                result = await self.place_order(
                    hedge_params["instId"],
                    hedge_params["tdMode"], 
                    hedge_params["side"],
                    hedge_params["ordType"],
                    hedge_params["sz"],
                    px=""  # 市价单不需要价格
                )
                
                if result.get('code') == '0':
                    order_id = result['data'][0]['ordId']
                    # 等待对冲订单成交
                    hedge_fill = await self._wait_for_order_fill(
                        order_id, spot_params["inst_id"], timeout=5
                    )
                    
                    if hedge_fill['success']:
                        return {
                            'success': True,
                            'direction': hedge_direction,
                            'amount': hedge_amount,
                            'filled_amount': hedge_fill['filled_size'],
                            'avg_price': hedge_fill['avg_price'],
                            'order_id': order_id
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"对冲订单未成交: {hedge_fill.get('error', 'timeout')}",
                            'order_id': order_id
                        }
                else:
                    return {
                        'success': False,
                        'error': f"对冲下单失败: {result.get('msg', 'unknown error')}"
                    }
                    
            elif hedge_direction == "close_futures":
                # 期货成交多了，需要平仓多余的期货头寸
                # 按交易对精度调整对冲数量
                min_sz = futures_precision.get('minSz', 1e-8)
                adjusted_amount = max(hedge_amount, min_sz)
                
                # 确保数量符合交易对规格 - 修复精度判断逻辑
                if hedge_amount < min_sz:
                    self.logger.warning(f"期货对冲数量{hedge_amount}小于最小交易单位{min_sz}，跳过对冲")
                    return {
                        'success': False,
                        'error': f"对冲数量太小: {hedge_amount} < {min_sz}",
                        'skipped': True
                    }
                
                # 获取原始期货订单的方向来确定平仓方向
                original_side = futures_params.get("side", "buy")
                close_side = "sell" if original_side == "buy" else "buy"
                
                hedge_params = {
                    "instId": futures_params["inst_id"],
                    "tdMode": "cross",  # 全仓模式
                    "side": close_side,
                    "ordType": "market",
                    "sz": f"{adjusted_amount:.8f}".rstrip('0').rstrip('.'),
                    "reduceOnly": True  # 只减仓，确保是平仓
                }
                
                self.logger.debug(f"📤 期货对冲参数: {hedge_params}")
                
                result = await self.place_order(
                    hedge_params["instId"],
                    hedge_params["tdMode"], 
                    hedge_params["side"],
                    hedge_params["ordType"],
                    hedge_params["sz"],
                    px=""  # 市价单不需要价格
                )
                
                if result.get('code') == '0':
                    order_id = result['data'][0]['ordId']
                    # 等待对冲订单成交
                    hedge_fill = await self._wait_for_order_fill(
                        order_id, futures_params["inst_id"], timeout=5
                    )
                    
                    if hedge_fill['success']:
                        return {
                            'success': True,
                            'direction': hedge_direction,
                            'amount': hedge_amount,
                            'filled_amount': hedge_fill['filled_size'],
                            'avg_price': hedge_fill['avg_price'],
                            'order_id': order_id
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"对冲订单未成交: {hedge_fill.get('error', 'timeout')}",
                            'order_id': order_id
                        }
                else:
                    return {
                        'success': False,
                        'error': f"对冲下单失败: {result.get('msg', 'unknown error')}"
                    }
            else:
                return {
                    'success': False,
                    'error': f"不支持的对冲方向: {hedge_direction}"
                }
                
        except Exception as e:
            self.logger.error(f"头寸对冲过程中出错: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _cancel_failed_atomic_orders(self, spot_result, futures_result, spot_params: Dict, futures_params: Dict):
        """取消失败的原子性订单"""
        try:
            # 如果现货下单成功但期货失败，取消现货订单
            if (not isinstance(spot_result, Exception) and spot_result.get('code') == '0' and
                (isinstance(futures_result, Exception) or futures_result.get('code') != '0')):
                
                spot_order_id = spot_result['data'][0]['ordId']
                await self.cancel_order(spot_params["inst_id"], spot_order_id)
                self.logger.info(f"已取消现货订单: {spot_order_id}")
            
            # 如果期货下单成功但现货失败，取消期货订单
            if (not isinstance(futures_result, Exception) and futures_result.get('code') == '0' and
                (isinstance(spot_result, Exception) or spot_result.get('code') != '0')):
                
                futures_order_id = futures_result['data'][0]['ordId']
                await self.cancel_order(futures_params["inst_id"], futures_order_id)
                self.logger.info(f"已取消期货订单: {futures_order_id}")
                
        except Exception as e:
            self.logger.error(f"取消失败订单时出错: {e}")
    
    async def _enhanced_atomic_rollback(self, spot_fill_result: Dict, futures_fill_result: Dict,
                                      spot_order_id: str, futures_order_id: str,
                                      spot_params: Dict, futures_params: Dict) -> Dict[str, Any]:
        """增强的原子性交易回滚机制 - 处理部分成交情况"""
        try:
            rollback_actions = []
            all_success = True
            
            # 处理现货部分成交回滚
            if spot_fill_result['success'] and spot_fill_result.get('filled_size', 0) > 0:
                # 现货有部分成交，需要对冲
                filled_size = spot_fill_result['filled_size']
                self.logger.info(f"🔄 现货部分成交回滚: {filled_size}")
                
                # 执行反向市价单对冲
                opposite_side = "sell" if spot_params.get("side") == "buy" else "buy"
                rollback_result = await self.place_order(
                    spot_params["inst_id"],
                    "cash",
                    opposite_side,
                    "market",
                    str(filled_size),
                    px=""  # 市价单不需要价格
                )
                
                if rollback_result.get('code') == '0':
                    rollback_order_id = rollback_result['data'][0]['ordId']
                    # 等待回滚成交
                    rollback_fill = await self._wait_for_order_fill(
                        rollback_order_id, spot_params["inst_id"], timeout=10
                    )
                    
                    if rollback_fill['success']:
                        rollback_actions.append(f"现货回滚成功: {opposite_side} {rollback_fill['filled_size']}")
                        self.logger.info(f"✅ 现货回滚成交: {rollback_fill['filled_size']}")
                    else:
                        all_success = False
                        rollback_actions.append(f"现货回滚失败: {rollback_fill.get('error', 'timeout')}")
                        self.logger.error(f"❌ 现货回滚失败: {rollback_fill.get('error', 'timeout')}")
                else:
                    all_success = False
                    rollback_actions.append(f"现货回滚下单失败: {rollback_result.get('msg', 'unknown')}")
                    self.logger.error(f"❌ 现货回滚下单失败: {rollback_result.get('msg', 'unknown')}")
            
            # 处理期货部分成交回滚
            if futures_fill_result['success'] and futures_fill_result.get('filled_size', 0) > 0:
                # 期货有部分成交，需要平仓
                filled_size = futures_fill_result['filled_size']
                self.logger.info(f"🔄 期货部分成交回滚: {filled_size}")
                
                # 执行反向平仓
                original_side = futures_params.get("side", "buy")
                close_side = "sell" if original_side == "buy" else "buy"
                
                rollback_result = await self.place_order(
                    futures_params["inst_id"],
                    "cross",
                    close_side,
                    "market",
                    str(filled_size),
                    px=""  # 市价单不需要价格
                )
                
                if rollback_result.get('code') == '0':
                    rollback_order_id = rollback_result['data'][0]['ordId']
                    # 等待回滚成交
                    rollback_fill = await self._wait_for_order_fill(
                        rollback_order_id, futures_params["inst_id"], timeout=10
                    )
                    
                    if rollback_fill['success']:
                        rollback_actions.append(f"期货回滚成功: {close_side} {rollback_fill['filled_size']}")
                        self.logger.info(f"✅ 期货回滚成交: {rollback_fill['filled_size']}")
                    else:
                        all_success = False
                        rollback_actions.append(f"期货回滚失败: {rollback_fill.get('error', 'timeout')}")
                        self.logger.error(f"❌ 期货回滚失败: {rollback_fill.get('error', 'timeout')}")
                else:
                    all_success = False
                    rollback_actions.append(f"期货回滚下单失败: {rollback_result.get('msg', 'unknown')}")
                    self.logger.error(f"❌ 期货回滚下单失败: {rollback_result.get('msg', 'unknown')}")
            
            # 取消未成交的订单
            if not spot_fill_result['success']:
                try:
                    await self.cancel_order(spot_params["inst_id"], spot_order_id)
                    rollback_actions.append(f"取消现货订单: {spot_order_id}")
                    self.logger.info(f"✅ 已取消现货订单: {spot_order_id}")
                except Exception as e:
                    rollback_actions.append(f"取消现货订单失败: {str(e)}")
                    self.logger.warning(f"⚠️ 取消现货订单失败: {e}")
            
            if not futures_fill_result['success']:
                try:
                    await self.cancel_order(futures_params["inst_id"], futures_order_id)
                    rollback_actions.append(f"取消期货订单: {futures_order_id}")
                    self.logger.info(f"✅ 已取消期货订单: {futures_order_id}")
                except Exception as e:
                    rollback_actions.append(f"取消期货订单失败: {str(e)}")
                    self.logger.warning(f"⚠️ 取消期货订单失败: {e}")
            
            return {
                'success': all_success,
                'actions': rollback_actions,
                'total_actions': len(rollback_actions)
            }
            
        except Exception as e:
            self.logger.error(f"增强回滚机制异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'actions': []
            }
    
    
    def __repr__(self):
        return (
            f"OKXConnector("
            f"public={self.is_public_connected}, "
            f"private={self.is_private_connected}, "
            f"authenticated={self.is_private_authenticated}, "
            f"sandbox={self.config.USE_SANDBOX}"
            f")"
        )