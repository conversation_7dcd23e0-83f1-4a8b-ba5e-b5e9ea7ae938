#!/usr/bin/env python3
"""
测试仪表盘完全依赖Redis数据
验证移除OKX API调用后的功能
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
import random
import time

async def test_redis_only_data_flow():
    """测试仅使用Redis数据的仪表盘"""
    print("🧪 测试仪表盘Redis数据流（无OKX API调用）...")
    
    # 连接Redis
    try:
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        await redis_client.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return
    
    # 配置的交易对
    trading_pairs = [
        {"spot_id": "BTC-USDT", "futures_id": "BTC-USDT-SWAP"},
        {"spot_id": "ETH-USDT", "futures_id": "ETH-USDT-SWAP"},
        {"spot_id": "DOGE-USDT", "futures_id": "DOGE-USDT-SWAP"}
    ]
    
    print(f"\n📊 为 {len(trading_pairs)} 个交易对发送完整的Redis数据...")
    
    # 持续发送数据，模拟交易机器人的数据流
    for round_num in range(20):  # 发送20轮数据
        print(f"\n🔄 第 {round_num + 1} 轮数据更新...")
        
        for pair_config in trading_pairs:
            pair_key = f"{pair_config['spot_id']}-{pair_config['futures_id']}"
            
            # 生成模拟价格数据
            base_price = 45000 if "BTC" in pair_key else (3000 if "ETH" in pair_key else 0.08)
            spot_price = base_price + random.uniform(-base_price*0.02, base_price*0.02)
            futures_price = spot_price + random.uniform(-base_price*0.001, base_price*0.002)
            
            # 计算基差
            basis = (futures_price - spot_price) / spot_price
            
            # 生成资金费率
            funding_rate = random.uniform(-0.0001, 0.0001)
            
            # 生成布林带数据
            bb_middle = basis * random.uniform(0.7, 1.3)
            bb_upper = bb_middle + abs(bb_middle) * 0.5
            bb_lower = bb_middle - abs(bb_middle) * 0.3
            
            # 1. 发送市场状态数据
            market_data = {
                "event_type": "market_state",
                "payload": {
                    "pair_name": pair_key,
                    "market_state": {
                        "basis": basis,
                        "spot_price": spot_price,
                        "futures_price": futures_price,
                        "funding_rate": funding_rate,
                        "bb_middle": bb_middle,
                        "bb_upper": bb_upper,
                        "bb_lower": bb_lower,
                        "is_realtime": True,
                        "update_source": "redis_only_test"
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await redis_client.publish('trading_bot:market', json.dumps(market_data))
            
            # 2. 发送微观结构数据
            obi = random.uniform(-0.8, 0.8)
            buy_sell_ratio = random.uniform(0.5, 2.0)
            micro_confidence = random.uniform(0.3, 0.95)
            
            micro_signal = "bullish" if obi > 0.3 else ("bearish" if obi < -0.3 else "neutral")
            obi_signal = "buy" if obi > 0.3 else ("sell" if obi < -0.3 else "neutral")
            flow_signal = "inflow" if buy_sell_ratio > 1.2 else ("outflow" if buy_sell_ratio < 0.8 else "neutral")
            
            micro_data = {
                "event_type": "microstructure_update",
                "payload": {
                    "pair_name": pair_key,
                    "micro_data": {
                        "obi": obi,
                        "buy_sell_ratio": buy_sell_ratio,
                        "micro_signal": micro_signal,
                        "micro_confidence": micro_confidence,
                        "obi_signal": obi_signal,
                        "flow_signal": flow_signal
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await redis_client.publish('trading_bot:microstructure', json.dumps(micro_data))
            
            print(f"📈 {pair_key}: 现货=${spot_price:.5f}, 期货=${futures_price:.5f}, "
                  f"基差={basis*100:.5f}%, 资金费率={funding_rate*100:.5f}%")
        
        # 3. 发送系统状态
        if round_num % 5 == 0:  # 每5轮发送一次系统状态
            system_data = {
                "event_type": "system_status",
                "payload": {
                    "status": "Running",
                    "details": {
                        "start_time": datetime.now().isoformat(),
                        "runtime_hours": round_num * 0.1,
                        "trade_pair": "ALL",
                        "subscribed_instruments": len(trading_pairs) * 2,
                        "position_active": random.choice([True, False]),
                        "connector_healthy": True,
                        "data_fresh": True,
                        "market_health_score": random.uniform(80, 95),
                        "emergency_stop": False,
                        "data_source": "redis_only"
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await redis_client.publish('trading_bot:system', json.dumps(system_data))
            print("📊 发送系统状态更新")
        
        # 4. 随机发送警报
        if random.random() < 0.3:  # 30%概率发送警报
            alert_levels = ["INFO", "WARNING", "ERROR"]
            alert_messages = [
                "市场数据更新正常",
                "检测到价格波动加剧",
                "基差偏离预期范围"
            ]
            
            level = random.choice(alert_levels)
            message = random.choice(alert_messages)
            
            alert_data = {
                "event_type": "alert",
                "payload": {
                    "level": level,
                    "message": f"{message} (轮次 {round_num + 1})",
                    "details": {
                        "component": "redis_only_test",
                        "round": round_num + 1
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await redis_client.publish('trading_bot:alerts', json.dumps(alert_data))
            print(f"⚠️ 发送 {level} 警报: {message}")
        
        # 5. 随机发送交易信号
        if random.random() < 0.2:  # 20%概率发送交易信号
            pair_key = random.choice([f"{p['spot_id']}-{p['futures_id']}" for p in trading_pairs])
            
            signal_data = {
                "event_type": "signal_generated",
                "payload": {
                    "pair_name": pair_key,
                    "signal_data": {
                        "signal_type": random.choice(["entry_signal", "exit_signal", "risk_warning"]),
                        "confidence": random.uniform(0.6, 0.95),
                        "risk_grade": random.choice(["Low", "Medium", "High"]),
                        "expected_profit": random.uniform(5.0, 25.0)
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await redis_client.publish('trading_bot:market', json.dumps(signal_data))
            print(f"🎯 发送交易信号: {pair_key}")
        
        await asyncio.sleep(2)  # 每2秒更新一次
    
    await redis_client.close()
    print("\n✅ Redis数据流测试完成！")
    print("\n📱 验证要点：")
    print("   1. 🔢 所有数值都应该显示5位小数精度")
    print("   2. 📈 市场数据应该每2秒实时更新")
    print("   3. 🎯 微观结构面板显示三层架构")
    print("   4. ⚠️ 系统警报面板有实时警报")
    print("   5. 📊 系统状态面板显示运行信息")
    print("   6. 🚫 仪表盘不应该有任何OKX API调用错误")

async def verify_no_okx_calls():
    """验证仪表盘不再调用OKX API"""
    print("\n🔍 验证仪表盘代码中是否移除了OKX API调用...")
    
    try:
        with open('trading_dashboard.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有OKX相关的调用
        okx_patterns = [
            'from okx_connector import',
            'okx_connector =',
            'okx_connector.get_',
            'await okx_connector.',
            'market_data_updater()',
            'update_market_data()'
        ]
        
        found_issues = []
        for pattern in okx_patterns:
            if pattern in content and not content.count(f'# {pattern}') and not content.count(f'# from okx_connector'):
                found_issues.append(pattern)
        
        if found_issues:
            print(f"❌ 发现残留的OKX调用: {found_issues}")
        else:
            print("✅ 确认：仪表盘代码中已完全移除OKX API调用")
            
    except Exception as e:
        print(f"❌ 检查代码失败: {e}")

if __name__ == "__main__":
    print("🧪 Redis数据流专用仪表盘测试")
    print("=" * 60)
    
    asyncio.run(verify_no_okx_calls())
    asyncio.run(test_redis_only_data_flow())
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！现在启动仪表盘验证：")
    print("   python trading_dashboard.py")
    print("\n💡 仪表盘现在完全依赖Redis数据：")
    print("   • ✅ 移除了所有OKX API调用")
    print("   • ✅ 所有数据来源于Redis消息")
    print("   • ✅ 不再有API限制和延迟问题")
    print("   • ✅ 更好的性能和稳定性")
