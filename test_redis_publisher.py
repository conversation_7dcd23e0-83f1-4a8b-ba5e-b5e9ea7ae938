#!/usr/bin/env python3
"""
测试Redis发布器是否正常工作
"""

import asyncio
import json
from redis_publisher import get_redis_publisher

async def test_redis_publishing():
    """测试Redis发布功能"""
    print("🧪 测试Redis发布器...")
    
    try:
        # 获取Redis发布器
        redis_publisher = await get_redis_publisher()
        print("✅ Redis发布器初始化成功")
        
        # 测试发布市场数据
        test_market_state = {
            "basis": 0.001234,
            "spot_price": 45000.12345,
            "futures_price": 45055.67890,
            "funding_rate": 0.000123,
            "bb_middle": 0.001100,
            "bb_upper": 0.001500,
            "bb_lower": 0.000700,
            "strategic_bb_middle": 0.001100,
            "strategic_bb_upper": 0.001500,
            "strategic_bb_lower": 0.000700,
            "tactical_bb_middle": 0.001050,
            "tactical_bb_upper": 0.001350,
            "tactical_bb_lower": 0.000750,
            "update_source": "test_script",
            "is_realtime": True,
            "strategy_state": "test",
            "data_points": 25
        }
        
        print("📤 发布测试市场数据...")
        await redis_publisher.publish_market_data("TEST_PAIR", test_market_state)
        print("✅ 市场数据发布成功")
        
        # 测试发布系统状态
        print("📤 发布测试系统状态...")
        await redis_publisher.publish_system_status("Testing", {"component": "test_script"})
        print("✅ 系统状态发布成功")
        
        print("\n🎉 Redis发布器测试完成，所有功能正常")
        
    except Exception as e:
        print(f"❌ Redis发布器测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_redis_publishing())
