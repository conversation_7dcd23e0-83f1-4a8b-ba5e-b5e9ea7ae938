#!/usr/bin/env python3
"""
重启交易机器人脚本
用于应用代码修改
"""

import subprocess
import time
import signal
import os
import sys

def find_bot_processes():
    """查找正在运行的机器人进程"""
    try:
        result = subprocess.run([
            'ps', 'aux'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            bot_processes = []
            
            for line in lines:
                if 'main.py' in line and 'python' in line and 'grep' not in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        bot_processes.append({
                            'pid': int(pid),
                            'command': ' '.join(parts[10:])
                        })
            
            return bot_processes
        else:
            print(f"❌ 查找进程失败: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"❌ 查找进程异常: {e}")
        return []

def stop_bot_processes(processes):
    """停止机器人进程"""
    if not processes:
        print("ℹ️ 没有找到运行中的机器人进程")
        return True
    
    print(f"🔍 找到 {len(processes)} 个机器人进程:")
    for proc in processes:
        print(f"   PID {proc['pid']}: {proc['command']}")
    
    print("\n⏹️ 停止机器人进程...")
    
    for proc in processes:
        try:
            pid = proc['pid']
            print(f"   停止 PID {pid}...")
            
            # 发送SIGTERM信号
            os.kill(pid, signal.SIGTERM)
            
            # 等待进程结束
            for i in range(10):  # 等待最多10秒
                try:
                    os.kill(pid, 0)  # 检查进程是否还存在
                    time.sleep(1)
                except OSError:
                    print(f"   ✅ PID {pid} 已停止")
                    break
            else:
                # 如果进程还没停止，强制杀死
                print(f"   ⚠️ PID {pid} 未响应SIGTERM，强制停止...")
                os.kill(pid, signal.SIGKILL)
                time.sleep(1)
                print(f"   ✅ PID {pid} 已强制停止")
                
        except OSError as e:
            if e.errno == 3:  # No such process
                print(f"   ✅ PID {pid} 已经停止")
            else:
                print(f"   ❌ 停止 PID {pid} 失败: {e}")
        except Exception as e:
            print(f"   ❌ 停止 PID {pid} 异常: {e}")
    
    return True

def start_bot():
    """启动机器人"""
    print("\n🚀 启动交易机器人...")
    
    try:
        # 启动机器人进程
        process = subprocess.Popen([
            'python', 'main.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"✅ 机器人已启动，PID: {process.pid}")
        
        # 等待几秒钟检查启动状态
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 机器人启动成功，正在运行中")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 机器人启动失败")
            if stdout:
                print(f"标准输出: {stdout}")
            if stderr:
                print(f"错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动机器人异常: {e}")
        return False

def verify_restart():
    """验证重启是否成功"""
    print("\n🔍 验证重启结果...")
    
    # 等待几秒让机器人完全启动
    time.sleep(5)
    
    # 检查新进程
    new_processes = find_bot_processes()
    if new_processes:
        print(f"✅ 发现 {len(new_processes)} 个新的机器人进程:")
        for proc in new_processes:
            print(f"   PID {proc['pid']}: {proc['command']}")
        
        # 检查日志文件是否有新的条目
        try:
            with open('arbitrage_bot_structured.log', 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1]
                    if '📡 发布市场状态到Redis' in last_line or '✅ 成功发布市场状态' in last_line:
                        print("✅ 检测到新的Redis发布日志，代码修改已生效")
                        return True
                    else:
                        print("ℹ️ 暂未检测到Redis发布日志，可能需要等待价格更新")
                        return True
        except Exception as e:
            print(f"⚠️ 检查日志文件失败: {e}")
        
        return True
    else:
        print("❌ 没有发现新的机器人进程")
        return False

def main():
    """主重启流程"""
    print("🔄 交易机器人重启脚本")
    print("=" * 50)
    
    # 1. 查找现有进程
    print("1️⃣ 查找现有机器人进程...")
    current_processes = find_bot_processes()
    
    # 2. 停止现有进程
    print("\n2️⃣ 停止现有进程...")
    if stop_bot_processes(current_processes):
        print("✅ 进程停止完成")
    else:
        print("❌ 进程停止失败")
        return False
    
    # 3. 等待一下确保进程完全停止
    print("\n⏳ 等待进程完全停止...")
    time.sleep(2)
    
    # 4. 启动新进程
    print("\n3️⃣ 启动新进程...")
    if start_bot():
        print("✅ 机器人启动完成")
    else:
        print("❌ 机器人启动失败")
        return False
    
    # 5. 验证重启结果
    print("\n4️⃣ 验证重启结果...")
    if verify_restart():
        print("\n🎉 机器人重启成功！")
        print("\n💡 建议:")
        print("   1. 运行 'python monitor_redis_realtime.py' 监控数据流")
        print("   2. 运行 'python trading_dashboard.py' 启动仪表盘")
        print("   3. 检查仪表盘是否显示实时数据")
        return True
    else:
        print("\n❌ 机器人重启验证失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 重启过程被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 重启过程异常: {e}")
        sys.exit(1)
