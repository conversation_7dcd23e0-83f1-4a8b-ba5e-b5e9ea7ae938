"""
异步套利机器人引擎 - 事件驱动的高性能交易系统
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Set, Tuple
import json

from okx_connector import OKXConnector
from multi_pair_strategy_manager import MultiPairStrategyManager
from position_manager import PositionManager, PositionType, OrderInfo
from okx_microstructure_adapter import OKXMicrostructureAdapter

from log_utils import get_structured_logger, trace_manager, KPICalculator
from config import get_enabled_trading_pairs, get_all_symbols
from redis_publisher import get_redis_publisher
# 监控系统已移除 - 性能指标通过log_utils.py的结构化日志记录


class ArbitrageBot:
    """事件驱动套利机器人 - 多交易对单边套利架构"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_structured_logger(__name__)
        
        # 核心组件
        self.connector = OKXConnector(config)
        self.strategy_manager = MultiPairStrategyManager(config)  # 替换单一策略为多交易对管理器
        self.position_manager = PositionManager(config)
        
        # 微观结构分析组件
        self.microstructure_adapter = OKXMicrostructureAdapter()
        self.strategy_manager.set_microstructure_adapters(self.microstructure_adapter)


        
        # ======= 多交易对实时状态管理 =======
        # 各交易对价格数据 {symbol: price}
        self.spot_prices: Dict[str, float] = {}
        self.futures_prices: Dict[str, float] = {}
        self.last_price_updates: Dict[str, datetime] = {}
        
        # 各交易对资金费率 {futures_symbol: rate}
        self.funding_rates: Dict[str, float] = {}
        self.last_funding_updates: Dict[str, datetime] = {}
        
        # 持仓状态（由WebSocket推送更新）
        self.current_positions: Dict[str, Dict[str, Any]] = {}
        self.last_position_update_time: Optional[datetime] = None
        
        # 订单状态（由WebSocket推送更新）
        self.open_orders: Dict[str, Dict[str, Any]] = {}
        self.last_order_update_time: Optional[datetime] = None
        
        # 账户状态（由WebSocket推送更新）
        self.account_balance: Dict[str, Any] = {}
        self.last_balance_update_time: Optional[datetime] = None
        
        # ======= 运行状态控制 =======
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # 性能监控
        self.message_count = 0
        self.processing_times = []
        
        # 价格更新日志控制
        self.price_log_counter = 0
        self.price_log_interval = 50
        
        # 风险控制
        self.emergency_stop = False
        self.error_count = 0
        self.max_errors = 10
        
        # 并发开仓锁定机制
        self.is_opening_position = False
        
        # 多交易对订阅列表
        spot_symbols, futures_symbols = get_all_symbols()
        self.subscribed_spot_instruments = spot_symbols
        self.subscribed_futures_instruments = futures_symbols
        all_instruments = spot_symbols + futures_symbols
        
        self.logger.info(
            f"多交易对套利机器人初始化完成 - "
            f"现货:{len(spot_symbols)}个, 期货:{len(futures_symbols)}个"
        )

        # Redis发布器（仪表盘通信）
        self.redis_publisher = None

        # 监控系统已移除 - 使用log_utils.py的结构化日志记录
        self.logger.info("📊 使用结构化日志系统进行性能监控")
    
    async def start(self):
        """启动事件驱动机器人"""
        try:
            self.logger.info("🚀 启动事件驱动套利机器人...")
            
            # 第一步：初始化Redis发布器
            self.logger.info("📡 初始化Redis发布器...")
            try:
                self.redis_publisher = await get_redis_publisher()
                await self.redis_publisher.publish_system_status("Initializing", {"component": "arbitrage_bot"})
                self.logger.info("✅ Redis发布器初始化成功")
            except Exception as e:
                self.logger.warning(f"⚠️ Redis发布器初始化失败，仪表盘将不可用: {e}")
                self.redis_publisher = None
            
            # 第二步：尝试恢复上次的状态
            self.logger.info("🔄 尝试恢复上次的仓位状态...")
            recovery_success = self.position_manager.load_state_from_json()
            
            if recovery_success:
                self.logger.info("✅ 仓位状态恢复成功")
                if self.position_manager.in_position:
                    position_info = self.position_manager.get_position_info()
                    self.logger.warning(
                        f"⚠️  检测到活跃仓位: {position_info['position_id']}, "
                        f"类型: {position_info['position_type']}, "
                        f"持仓时长: {position_info['holding_duration_seconds']/3600:.1f}小时"
                    )
                    # 发布仓位恢复事件
                    if self.redis_publisher:
                        await self.redis_publisher.publish_position_event(
                            "position_recovered", 
                            position_info['position_id'],
                            position_info
                        )
            else:
                self.logger.info("🆕 使用全新状态启动")
            
            # 启动双通道连接器
            await self.connector.start()
            
            # 初始化交易对规格信息
            self.logger.info("📋 初始化交易对规格信息...")
            specs_initialized = await self.connector.initialize_instrument_specs()
            if not specs_initialized:
                raise RuntimeError("交易对规格初始化失败，无法继续运行")
            
            # 注册所有事件回调函数
            self._register_event_callbacks()
            
            # 订阅所有需要的数据流
            await self._subscribe_all_channels()
            
            # 设置运行状态
            self.is_running = True
            self.start_time = datetime.now()
            
            # 发布系统启动完成事件
            if self.redis_publisher:
                try:
                    system_details = {
                        "start_time": self.start_time.isoformat(),
                        "trade_pair": self.config.TRADE_PAIR,
                        "subscribed_instruments": len(self.subscribed_spot_instruments + self.subscribed_futures_instruments),
                        "position_recovered": self.position_manager.in_position
                    }
                    await self.redis_publisher.publish_system_status(
                        "Running", 
                        system_details
                    )
                    self.logger.info("📡 系统状态已发布到Redis")
                except Exception as redis_e:
                    self.logger.debug(f"发布系统启动状态到Redis失败: {redis_e}")
            
            # 设置手动平仓检测
            self.position_manager.set_okx_connector(self.connector)
            
            self.logger.info("✅ 事件驱动机器人启动成功，等待实时数据推送")
            
            # 保持运行（事件驱动，无需主循环）
            try:
                while self.is_running:
                    await asyncio.sleep(1)  # 最小休眠，主要工作由事件回调处理
                    
                    # 定期检测手动平仓
                    manual_close_detected = await self.position_manager.check_manual_close_detection()
                    if manual_close_detected:
                        self.logger.info("🔄 检测到手动平仓，系统状态已自动同步")
                    
                    # 定期健康检查（非轮询业务逻辑）
                    if self.start_time and (datetime.now() - self.start_time).total_seconds() % 300 == 0:
                        await self._perform_health_check()
            except KeyboardInterrupt:
                self.logger.info("收到停止信号")
            
        except Exception as e:
            self.logger.error(f"机器人启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止事件驱动机器人"""
        try:
            self.logger.info("🛑 正在停止事件驱动机器人...")
            
            self.is_running = False
            
            # 如果有开仓位置，尝试平仓
            if self.position_manager.in_position:
                self.logger.warning("检测到未平仓位，尝试紧急平仓")
                await self._emergency_close_position("系统停止")
            
            # 停止双通道连接器
            await self.connector.stop()
            
            # 输出最终统计
            self._log_final_statistics()
            
            self.logger.info("✅ 事件驱动机器人已停止")
            
        except Exception as e:
            self.logger.error(f"停止机器人时发生错误: {e}")
    
    def _register_event_callbacks(self):
        """注册所有事件回调函数"""
        try:
            self.logger.info("📋 注册事件回调函数...")
            
            # 注册公共频道回调
            self.connector.register_event_callback('ticker', self.handle_ticker_update)
            self.connector.register_event_callback('books', self.handle_books_update)
            self.connector.register_event_callback('trades', self.handle_trades_update)  # 微观结构：逐笔成交
            
            # 注册资金费率回调（关键：实时净利润计算）
            self.connector.register_event_callback('funding-rate', self.handle_funding_rate_update)
            
            # 注册私有频道回调
            self.connector.register_event_callback('account', self.handle_account_update)
            self.connector.register_event_callback('orders', self.handle_order_update)
            self.connector.register_event_callback('positions', self.handle_position_update)
            
            self.logger.info("✅ 事件回调函数注册完成")
            
        except Exception as e:
            self.logger.error(f"注册事件回调失败: {e}")
            raise
    
    async def _subscribe_all_channels(self):
        """订阅所有需要的数据流 - 多交易对版本"""
        try:
            self.logger.info("📡 订阅多交易对数据流...")
            
            # 订阅所有交易对的价格数据
            all_instruments = self.subscribed_spot_instruments + self.subscribed_futures_instruments
            await self.connector.subscribe_tickers(all_instruments)
            await self.connector.subscribe_order_books(all_instruments)
            
            # 订阅所有现货的微观结构数据（高频分析）
            await self.connector.subscribe_order_books_5(self.subscribed_spot_instruments)
            await self.connector.subscribe_trades(self.subscribed_spot_instruments)
            
            # 订阅所有期货的资金费率（关键：实时净利润计算）
            await self.connector.subscribe_funding_rates(self.subscribed_futures_instruments)
            
            # 订阅私有频道数据
            await self.connector.subscribe_account_updates()
            await self.connector.subscribe_order_updates("SWAP")
            await self.connector.subscribe_position_updates("SWAP")
            
            self.logger.info(
                f"✅ 多交易对数据流订阅完成 - "
                f"总计{len(all_instruments)}个标的"
            )
            
        except Exception as e:
            self.logger.error(f"订阅数据流失败: {e}")
            raise
    
    # ======= WebSocket 事件回调处理函数 =======
    
    async def handle_ticker_update(self, data: Dict[str, Any]):
        """处理价格更新事件 - 多交易对版本"""
        try:
            start_time = time.time()
            
            # 解析价格数据
            inst_id = data["arg"]["instId"]
            ticker_data = data["data"][0]
            last_price = float(ticker_data["last"])
            current_time = datetime.now()
            
            # 更新价格状态
            price_updated = False
            
            if inst_id in self.subscribed_spot_instruments:
                old_price = self.spot_prices.get(inst_id)
                self.spot_prices[inst_id] = last_price
                self.last_price_updates[inst_id] = current_time
                price_updated = True
                
                # 查找对应的期货价格，更新策略
                for pair_status in self.strategy_manager.pair_strategies.values():
                    if pair_status.spot_symbol == inst_id:
                        futures_symbol = pair_status.futures_symbol
                        futures_price = self.futures_prices.get(futures_symbol)
                        
                        if futures_price:
                            self.strategy_manager.update_pair_prices(
                                inst_id, futures_symbol, last_price, futures_price
                            )
                            # 实时记录市场状态更新（WebSocket触发）
                            await self._log_market_state_update_realtime(inst_id, last_price, futures_price)
                        break
                        
            elif inst_id in self.subscribed_futures_instruments:
                old_price = self.futures_prices.get(inst_id)
                self.futures_prices[inst_id] = last_price
                self.last_price_updates[inst_id] = current_time
                price_updated = True
                
                # 查找对应的现货价格，更新策略
                for pair_status in self.strategy_manager.pair_strategies.values():
                    if pair_status.futures_symbol == inst_id:
                        spot_symbol = pair_status.spot_symbol
                        spot_price = self.spot_prices.get(spot_symbol)
                        
                        if spot_price:
                            self.strategy_manager.update_pair_prices(
                                spot_symbol, inst_id, spot_price, last_price
                            )
                            # 实时记录市场状态更新（WebSocket触发）
                            await self._log_market_state_update_realtime(spot_symbol, spot_price, last_price)
                        break
            
            if price_updated:
                self.last_price_update_time = datetime.now()
                self.message_count += 1
                self.price_log_counter += 1
                
                # 定期记录详细价格信息 (降级为DEBUG)
                if self.price_log_counter % self.price_log_interval == 0:
                    instrument_type = "spot" if inst_id in self.subscribed_spot_instruments else "futures"
                    self.logger.debug_structured(
                        f"{instrument_type}价格更新",
                        event_type="price_update",
                        metrics={
                            "price": last_price,
                            "price_change_pct": ((last_price-old_price)/old_price*100) if old_price else 0
                        },
                        details={"instrument": instrument_type, "symbol": inst_id}
                    )
                
                # 多交易对策略检查 - 当有足够价格数据时触发
                if self.price_log_counter % 100 == 0:  # 每100次价格更新记录一次
                    self.logger.info(f"🔄 触发策略检查 (#{self.price_log_counter})")
                await self._check_all_strategies_async()
            
            # 记录处理时间和Prometheus指标
            processing_time = (time.time() - start_time) * 1000
            self.processing_times.append(processing_time)
            if len(self.processing_times) > 1000:
                self.processing_times.pop(0)
            
            # 记录价格更新延迟指标
            # 性能指标通过结构化日志记录
            
            # 记录WebSocket消息
            # WebSocket消息统计通过结构化日志记录
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"处理Ticker更新失败: {e}")
            
            # 记录错误指标
            # 错误统计通过结构化日志记录
            # WebSocket消息统计通过结构化日志记录
    
    async def handle_books_update(self, data: Dict[str, Any]):
        """处理订单簿更新事件（含微观结构分析）"""
        try:
            inst_id = data["arg"]["instId"]
            books_data = data["data"][0]
            bids = books_data.get("bids", [])
            asks = books_data.get("asks", [])
            
            if len(bids) > 0 and len(asks) > 0:
                best_bid = float(bids[0][0])
                best_ask = float(asks[0][0])
                spread = (best_ask - best_bid) / best_bid
                
                # 处理微观结构分析（仅针对现货）
                if inst_id == self.config.SPOT_ID:
                    orderbook_snapshot = self.microstructure_adapter.process_orderbook_update(data)
                    
                    if orderbook_snapshot:
                        # 获取微观结构信号用于交易决策增强
                        microstructure_signals = self.microstructure_adapter.get_microstructure_signals(inst_id)
                        
                        # 只在信号变化或异常情况下记录详细日志
                        if microstructure_signals and microstructure_signals.get('micro_signal') != 'neutral':
                            self.logger.debug_structured(
                                f"微观结构信号更新 {inst_id}",
                                event_type="microstructure_signal_update",
                                metrics={
                                    'micro_signal': microstructure_signals.get('micro_signal'),
                                    'micro_confidence': microstructure_signals.get('micro_confidence'),
                                    'obi_signal': microstructure_signals.get('obi_signal'),
                                    'flow_signal': microstructure_signals.get('flow_signal')
                                },
                                details={'symbol': inst_id}
                            )

                            # 发布微观结构数据到Redis（用于仪表盘）
                            if self.redis_publisher:
                                try:
                                    # 查找对应的交易对名称
                                    pair_name = None
                                    for pname, pair_status in self.strategy_manager.pair_strategies.items():
                                        if inst_id in [pair_status.spot_symbol, pair_status.futures_symbol]:
                                            pair_name = pname
                                            break

                                    if pair_name:
                                        micro_data = {
                                            'obi': microstructure_signals.get('obi', 0.0),
                                            'buy_sell_ratio': microstructure_signals.get('buy_sell_ratio', 1.0),
                                            'micro_signal': microstructure_signals.get('micro_signal', 'neutral'),
                                            'micro_confidence': microstructure_signals.get('micro_confidence', 0.0),
                                            'obi_signal': microstructure_signals.get('obi_signal', 'neutral'),
                                            'flow_signal': microstructure_signals.get('flow_signal', 'neutral')
                                        }
                                        await self.redis_publisher.publish_microstructure_data(pair_name, micro_data)
                                except Exception as redis_e:
                                    self.logger.debug(f"发布微观结构数据到Redis失败: {redis_e}")
                
                # 记录市场状态用于仪表盘显示
                await self._log_market_state_for_symbol(inst_id)
                    
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error_structured(
                f"处理订单簿更新失败: {e}",
                event_type="books_update_error",
                details={
                    'error': str(e),
                    'raw_data': str(data)[:500]
                }
            )
            
            # 发布错误警报到Redis
            if self.redis_publisher:
                try:
                    await self.redis_publisher.publish_alert(
                        "ERROR",
                        f"处理订单簿更新失败: {e}",
                        {"component": "books_update", "error": str(e)}
                    )
                except Exception:
                    pass  # Redis发布失败不影响主要功能

    async def _log_market_state_for_symbol(self, symbol: str):
        """为指定交易对记录市场状态（用于仪表盘显示）"""
        try:
            # 获取对应的现货和期货价格
            spot_price = None
            futures_price = None

            if symbol in self.spot_prices:
                spot_price = self.spot_prices[symbol]
                # 查找对应的期货价格
                for pair_status in self.strategy_manager.pair_strategies.values():
                    if pair_status.spot_symbol == symbol:
                        futures_symbol = pair_status.futures_symbol
                        futures_price = self.futures_prices.get(futures_symbol)
                        break
            elif symbol in self.futures_prices:
                futures_price = self.futures_prices[symbol]
                # 查找对应的现货价格
                for pair_status in self.strategy_manager.pair_strategies.values():
                    if pair_status.futures_symbol == symbol:
                        spot_symbol = pair_status.spot_symbol
                        spot_price = self.spot_prices.get(spot_symbol)
                        break

            # 如果没有配对价格，跳过记录
            if not (spot_price and futures_price):
                return

            # 记录实时市场状态更新（用于仪表盘）
            await self._log_market_state_update_realtime(symbol, spot_price, futures_price)

        except Exception as e:
            self.logger.error(f"记录市场状态失败 {symbol}: {e}")



    async def _log_market_state_update_realtime(self, symbol: str, spot_price: float, futures_price: float):
        """实时记录市场状态更新事件（WebSocket触发）"""
        try:
            # 查找对应的交易对
            for pair_name, pair_status in self.strategy_manager.pair_strategies.items():
                if symbol in [pair_status.spot_symbol, pair_status.futures_symbol]:
                    # 获取策略状态 - 使用get_current_state()方法获取最新计算结果
                    strategy = pair_status.strategy
                    strategy_state = strategy.get_current_state()

                    # 使用策略计算的最新基差
                    current_basis = strategy_state.get('current_basis', 0.0)
                    if current_basis is None or current_basis == 0.0:
                        # 如果策略还没有基差数据，使用策略相同的计算公式
                        current_basis = (futures_price - spot_price) / spot_price if spot_price > 0 else 0.0

                    # 从策略状态获取分层布林带数据
                    layered_bb = strategy_state.get('layered_bollinger_bands', {})
                    strategic_bb = layered_bb.get('strategic', {})
                    tactical_bb = layered_bb.get('tactical', {})

                    # 分层布林带（战略层）- 主要用于仪表盘显示
                    strategic_bb_middle = strategic_bb.get('bb_middle', None)
                    strategic_bb_upper = strategic_bb.get('bb_upper', None)
                    strategic_bb_lower = strategic_bb.get('bb_lower', None)

                    # 分层布林带（战术层）
                    tactical_bb_middle = tactical_bb.get('bb_middle', None)
                    tactical_bb_upper = tactical_bb.get('bb_upper', None)
                    tactical_bb_lower = tactical_bb.get('bb_lower', None)

                    # 传统布林带（向后兼容）
                    bb_middle = strategic_bb_middle  # 使用战略层作为默认
                    bb_upper = strategic_bb_upper
                    bb_lower = strategic_bb_lower

                    # 记录结构化日志
                    self.logger.info_structured(
                        f"实时市场状态更新 - {pair_name}",
                        event_type="strategy_state_update",
                        metrics={
                            "current_basis": current_basis,
                            # 传统布林带
                            "bb_middle": bb_middle,
                            "bb_upper": bb_upper,
                            "bb_lower": bb_lower,
                            # 分层布林带 - 战略层（主要用于仪表盘显示）
                            "strategic_bb_middle": strategic_bb_middle,
                            "strategic_bb_upper": strategic_bb_upper,
                            "strategic_bb_lower": strategic_bb_lower,
                            # 分层布林带 - 战术层
                            "tactical_bb_middle": tactical_bb_middle,
                            "tactical_bb_upper": tactical_bb_upper,
                            "tactical_bb_lower": tactical_bb_lower,
                        },
                        details={
                            "pair_name": pair_name,
                            "symbol": symbol,
                            "spot_symbol": pair_status.spot_symbol,
                            "futures_symbol": pair_status.futures_symbol,
                            "spot_price": spot_price,
                            "futures_price": futures_price,
                            "update_source": "websocket_realtime",
                            # 策略状态信息
                            "strategy_state": getattr(strategy, 'strategy_state', 'unknown'),
                            "data_points": len(getattr(strategy, 'basis_history', [])),
                            "strategic_data_points": len(getattr(strategy, 'strategic_basis_history', [])),
                            "tactical_data_points": len(getattr(strategy, 'tactical_basis_history', []))
                        }
                    )
                    
                    # 发布Redis市场状态事件（用于仪表盘）
                    if self.redis_publisher:
                        try:
                            # 获取资金费率 - 使用WebSocket实时数据
                            funding_rate = 0.0
                            futures_symbol = pair_status.futures_symbol

                            # 优先使用WebSocket实时资金费率数据
                            if futures_symbol in self.funding_rates:
                                funding_rate = self.funding_rates[futures_symbol]
                                self.logger.debug(f"使用WebSocket资金费率: {futures_symbol} = {funding_rate:.6f}")
                            else:
                                # 如果WebSocket数据不可用，尝试API获取
                                try:
                                    if hasattr(self, 'connector') and self.connector:
                                        funding_data = await self.connector.get_funding_rate(futures_symbol)
                                        if funding_data:
                                            funding_rate = float(funding_data.get('fundingRate', 0))
                                            self.logger.debug(f"使用API资金费率: {futures_symbol} = {funding_rate:.6f}")
                                except Exception as e:
                                    self.logger.debug(f"获取资金费率失败: {e}")

                            market_state = {
                                "basis": current_basis,
                                "spot_price": spot_price,
                                "futures_price": futures_price,
                                "funding_rate": funding_rate,
                                "bb_middle": strategic_bb_middle or bb_middle,
                                "bb_upper": strategic_bb_upper or bb_upper,
                                "bb_lower": strategic_bb_lower or bb_lower,
                                "strategic_bb_middle": strategic_bb_middle,
                                "strategic_bb_upper": strategic_bb_upper,
                                "strategic_bb_lower": strategic_bb_lower,
                                "tactical_bb_middle": tactical_bb_middle,
                                "tactical_bb_upper": tactical_bb_upper,
                                "tactical_bb_lower": tactical_bb_lower,
                                "update_source": "websocket_realtime",
                                "is_realtime": True,
                                "strategy_state": strategy_state.get('strategy_state', {}).get('current_state', 'unknown'),
                                "data_points": len(strategy_state.get('basis_history', []))
                            }

                            # 关键调试：记录发布到Redis的完整数据
                            strategic_middle_str = f"{strategic_bb_middle:.6f}" if strategic_bb_middle is not None else "None"
                            strategic_upper_str = f"{strategic_bb_upper:.6f}" if strategic_bb_upper is not None else "None"

                            self.logger.info(f"📡 发布市场状态到Redis - {pair_name}: "
                                            f"基差={current_basis:.6f}, "
                                            f"现货=${spot_price:.5f}, 期货=${futures_price:.5f}, "
                                            f"资金费率={funding_rate:.6f}, "
                                            f"战略中轨={strategic_middle_str}, "
                                            f"战略上轨={strategic_upper_str}")

                            await self.redis_publisher.publish_market_data(pair_name, market_state)
                            self.logger.info(f"✅ 成功发布市场状态到Redis - {pair_name}")
                        except Exception as redis_e:
                            self.logger.error(f"❌ 发布市场状态到Redis失败: {redis_e}")
                    
                    break

        except Exception as e:
            self.logger.error(f"记录实时市场状态更新失败: {e}")







    async def handle_trades_update(self, data: Dict[str, Any]):
        """处理逐笔成交更新事件（微观结构分析）"""
        try:
            inst_id = data["arg"]["instId"]
            
            # 仅处理现货的逐笔成交数据
            if inst_id == self.config.SPOT_ID:
                trades = self.microstructure_adapter.process_trade_update(data)
                
                if trades:
                    # 统计成交信息
                    total_volume = sum(trade.size for trade in trades)
                    buy_volume = sum(trade.size for trade in trades if trade.side == 'buy')
                    sell_volume = sum(trade.size for trade in trades if trade.side == 'sell')
                    
                    # 高频交易流数据使用DEBUG级别避免日志洪水
                    self.logger.debug_structured(
                        f"逐笔成交数据 {inst_id} 批次处理",
                        event_type="trades_batch_processed",
                        metrics={
                            'trade_count': len(trades),
                            'total_volume': total_volume,
                            'buy_volume': buy_volume,
                            'sell_volume': sell_volume,
                            'buy_ratio': buy_volume / total_volume if total_volume > 0 else 0
                        },
                        details={'symbol': inst_id}
                    )
                    
                    # 获取更新后的交易流指标
                    indicators = self.microstructure_adapter.get_latest_indicators(inst_id)
                    
                    # 更新Prometheus交易压力指标
                    volume_pressure = indicators.get('volume_pressure', 0)
                    # 交易压力通过结构化日志记录
                    
                    # 只在交易压力显著偏向时记录DEBUG级别日志
                    if abs(volume_pressure) > 0.3:  # 压力超过30%时记录
                        pressure_type = '买压' if volume_pressure > 0 else '卖压'
                        self.logger.debug_structured(
                            f"检测到显著{pressure_type} {inst_id}",
                            event_type="significant_trade_pressure",
                            metrics={
                                'volume_pressure': volume_pressure,
                                'buy_sell_ratio': indicators.get('buy_sell_ratio'),
                                'trade_intensity': indicators.get('trade_intensity')
                            },
                            details={'symbol': inst_id}
                        )
                
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error_structured(
                f"处理逐笔成交更新失败: {e}",
                event_type="trades_update_error",
                details={
                    'error': str(e),
                    'raw_data': str(data)[:500]
                }
            )
    
    async def handle_funding_rate_update(self, data: Dict[str, Any]):
        """处理资金费率更新事件 - 多交易对版本"""
        try:
            funding_data = data.get("data", [])
            
            for funding_info in funding_data:
                inst_id = funding_info.get("instId")
                funding_rate = float(funding_info.get("fundingRate", 0))
                funding_time = funding_info.get("fundingTime")
                
                # 更新对应期货合约的资金费率
                if inst_id in self.subscribed_futures_instruments:
                    old_rate = self.funding_rates.get(inst_id, 0.0)
                    self.funding_rates[inst_id] = funding_rate
                    self.last_funding_updates[inst_id] = datetime.now()
                    
                    # 更新Prometheus资金费率指标
                    # 资金费率通过结构化日志记录
                    
                    self.logger.debug_structured(
                        "资金费率更新",
                        event_type="funding_rate_update",
                        metrics={
                            "funding_rate_bps": KPICalculator.funding_rate_bps(funding_rate),
                            "funding_rate_change_bps": (funding_rate - old_rate) * 10000,
                            "old_funding_rate": old_rate,
                            "new_funding_rate": funding_rate
                        },
                        details={
                            "inst_id": inst_id,
                            "funding_time": funding_time
                        }
                    )
                    
                    # 关键：资金费率更新后触发多交易对策略重新评估
                    await self._check_all_strategies_async()
                    
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"处理资金费率更新失败: {e}")
    
    async def handle_account_update(self, data: Dict[str, Any]):
        """处理账户更新事件"""
        try:
            # 更新账户余额状态
            account_data = data.get("data", [])
            for account_info in account_data:
                ccy = account_info.get("ccy")
                cash_bal = account_info.get("cashBal")
                if ccy and cash_bal:
                    self.account_balance[ccy] = {
                        "balance": float(cash_bal),
                        "update_time": datetime.now()
                    }
            
            self.last_balance_update_time = datetime.now()
            self.logger.debug(f"💰 账户余额更新: {len(account_data)} 条记录")
            
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"处理账户更新失赅: {e}")
    
    async def handle_position_update(self, data: Dict[str, Any]):
        """处理持仓更新事件（取代原有的轮询检查）"""
        try:
            position_data = data.get("data", [])
            
            for pos_info in position_data:
                inst_id = pos_info.get("instId")
                pos_size = float(pos_info.get("pos", 0))
                unrealized_pnl = float(pos_info.get("upl", 0))
                margin_ratio = float(pos_info.get("mgnRatio", 0))
                
                # 更新内部状态
                self.current_positions[inst_id] = {
                    "size": pos_size,
                    "unrealized_pnl": unrealized_pnl,
                    "margin_ratio": margin_ratio,
                    "update_time": datetime.now(),
                    "raw_data": pos_info
                }
                
                # 实时更新PositionManager的未实现盈亏（多交易对版本暂时跳过）
                # TODO: 需要根据持仓的具体交易对来更新PnL
                pass
                
                # 实时风险检查（取代轮询）
                if self.position_manager.in_position:
                    await self._check_position_risks_realtime(pos_info)
            
            self.last_position_update_time = datetime.now()
            self.logger.debug(f"🎯 持仓更新: {len(position_data)} 条记录")
            
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"处理持仓更新失败: {e}")
    
    async def handle_order_update(self, data: Dict[str, Any]):
        """处理订单更新事件（关键：PositionManager状态变更驱动）"""
        try:
            order_data = data.get("data", [])
            
            for order_info in order_data:
                order_id = order_info.get("ordId")
                inst_id = order_info.get("instId")
                state = order_info.get("state")
                side = order_info.get("side")
                filled_size = float(order_info.get("fillSz", 0))
                avg_price = float(order_info.get("avgPx", 0))
                
                # 更新内部订单状态
                self.open_orders[order_id] = {
                    "inst_id": inst_id,
                    "state": state,
                    "side": side,
                    "filled_size": filled_size,
                    "avg_price": avg_price,
                    "update_time": datetime.now(),
                    "raw_data": order_info
                }
                
                self.logger.info(
                    f"📱 订单状态更新: {order_id} ({inst_id}) {side} {state} "
                    f"filled:{filled_size} @ {avg_price}"
                )
                
                # 关键：仅在完全成交时才触发PositionManager状态变更
                if state == "filled":
                    await self._handle_order_filled_for_position_manager(order_info)
                elif state == "partially_filled":
                    self.logger.info(f"🔄 订单部分成交，等待完全成交: {order_id}")
                elif state == "canceled":
                    self.logger.warning(f"❌ 订单被取消: {order_id} - 可能影响仓位状态")
                    await self._handle_order_canceled(order_info)
            
            self.last_order_update_time = datetime.now()
            
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"处理订单更新失败: {e}")
    
    async def _handle_order_filled_for_position_manager(self, order_info: Dict[str, Any]):
        """处理订单成交事件（PositionManager状态变更驱动）"""
        try:
            order_id = order_info.get("ordId")
            inst_id = order_info.get("instId")
            side = order_info.get("side")
            filled_size = float(order_info.get("fillSz", 0))
            avg_price = float(order_info.get("avgPx", 0))
            
            self.logger.info(
                f"✅ 订单完全成交: {order_id} ({inst_id}) {side} {filled_size} @ {avg_price}"
            )
            
            # 关键：仅在有活跃仓位时才处理PositionManager状态变更
            if self.position_manager.current_position:
                current_status = self.position_manager.current_position.status.value
                
                if current_status == "opening":
                    # 开仓订单成交 - 只有在收到交易所确认时才更新状态
                    await self._handle_entry_order_filled_real_time(order_info)
                elif current_status == "closing":
                    # 平仓订单成交 - 只有在收到交易所确认时才更新状态
                    await self._handle_exit_order_filled_real_time(order_info)
                else:
                    self.logger.warning(
                        f"⚠️  意外的订单成交：仓位状态={current_status}, 订单={order_id}"
                    )
            else:
                self.logger.warning(
                    f"⚠️  没有活跃仓位时收到订单成交: {order_id} - 可能是对冲订单"
                )
            
            # 从未完成订单中移除
            if order_id in self.open_orders:
                del self.open_orders[order_id]
                
        except Exception as e:
            self.logger.error(f"处理订单成交驱动状态变更失败: {e}")
    
    async def _handle_order_canceled(self, order_info: Dict[str, Any]):
        """处理订单取消事件"""
        try:
            order_id = order_info.get("ordId")
            inst_id = order_info.get("instId")
            
            # 从未完成订单中移除
            if order_id in self.open_orders:
                del self.open_orders[order_id]
            
            # 如果有活跃仓位且是关键订单被取消，可能需要设置错误状态
            if (self.position_manager.current_position and 
                self.position_manager.current_position.status.value in ["opening", "closing"]):
                
                self.logger.error(
                    f"⚠️  关键订单被取消，可能导致仓位状态不一致: {order_id} ({inst_id})"
                )
                # 可能需要设置仓位为错误状态或触发紧急处理
                
        except Exception as e:
            self.logger.error(f"处理订单取消失败: {e}")
    
    async def _handle_entry_order_filled_real_time(self, order_info: Dict[str, Any]):
        """处理开仓订单成交（事件驱动版本）"""
        try:
            inst_id = order_info.get("instId")
            order_id = order_info.get("ordId")
            filled_size = float(order_info.get("fillSz", 0))
            avg_price = float(order_info.get("avgPx", 0))
            
            self.logger.info(
                f"🔄 开仓订单成交: {inst_id} {order_id} 成交{filled_size} @ {avg_price}"
            )
            
            # 更新PositionManager中的订单信息
            await self._update_position_order_info(order_info)
            
            # 检查是否两条腿都成交了（基于实时订单状态）
            both_legs_filled = await self._check_both_legs_filled_for_entry()
            
            if both_legs_filled:
                # 关键：只有在收到交易所真实成交确认时才调用confirm_position_open
                self.position_manager.confirm_position_open()
                
                # 发布开仓成功事件到Redis（用于仪表盘）
                if self.redis_publisher and self.position_manager.current_position:
                    try:
                        position_info = self.position_manager.get_position_info()
                        trade_data = {
                            "action": "position_opened",
                            "position_id": position_info['position_id'],
                            "position_type": position_info['position_type'],
                            "entry_basis": position_info['entry_basis'],
                            "entry_spot_price": position_info['entry_spot_price'],
                            "entry_futures_price": position_info['entry_futures_price'],
                            "open_time": position_info['open_time']
                        }
                        await self.redis_publisher.publish_trade_event(
                            "position_opened",
                            self.config.TRADE_PAIR,
                            trade_data
                        )
                    except Exception as redis_e:
                        self.logger.debug(f"发布开仓事件到Redis失败: {redis_e}")
                
                # 更新Prometheus持仓状态指标
                # 持仓状态通过结构化日志记录
                # 交易记录通过结构化日志记录
                
                self.logger.info(
                    f"🎉 套利开仓完成！两条腿都收到交易所成交确认"
                )
                
                # 状态已由PositionManager自动保存
                
                # 清理开仓锁定
                self.is_opening_position = False
                
                # 记录关键信息
                position_info = self.position_manager.get_position_info()
                self.logger.info(
                    f"📊 仓位已确认: {position_info['position_id']} "
                    f"{position_info['position_type']} 基差:{position_info.get('entry_basis', 'N/A'):.6f}"
                )
            else:
                self.logger.info(
                    f"🔄 单腿成交完成，等待另一条腿: {inst_id}"
                )
                
        except Exception as e:
            self.logger.error(f"处理开仓订单实时成交失败: {e}")
    
    async def _handle_exit_order_filled_real_time(self, order_info: Dict[str, Any]):
        """处理平仓订单成交（事件驱动版本）"""
        try:
            inst_id = order_info.get("instId")
            order_id = order_info.get("ordId")
            filled_size = float(order_info.get("fillSz", 0))
            avg_price = float(order_info.get("avgPx", 0))
            
            self.logger.info(
                f"🔄 平仓订单成交: {inst_id} {order_id} 成交{filled_size} @ {avg_price}"
            )
            
            # 检查是否两条腿都平仓完成（基于实时订单状态）
            both_legs_filled = await self._check_both_legs_filled_for_exit()
            
            if both_legs_filled:
                # 关键：只有在收到交易所真实成交确认时才调用confirm_position_close
                current_pos = self.position_manager.current_position
                realized_pnl = current_pos.unrealized_pnl if current_pos else 0.0
                
                # 保存平仓信息用于Redis发布
                position_data = {
                    "position_id": current_pos.position_id if current_pos else "unknown",
                    "realized_pnl": realized_pnl,
                    "close_reason": "manual_exit"  # 这里可以传入实际原因
                }
                
                self.position_manager.confirm_position_close(realized_pnl)
                
                # 发布平仓成功事件到Redis（用于仪表盘）
                if self.redis_publisher:
                    try:
                        trade_data = {
                            "action": "position_closed",
                            "position_id": position_data['position_id'],
                            "realized_pnl": realized_pnl,
                            "close_reason": position_data['close_reason'],
                            "close_time": datetime.now()
                        }
                        await self.redis_publisher.publish_trade_event(
                            "position_closed",
                            self.config.TRADE_PAIR,
                            trade_data
                        )
                    except Exception as redis_e:
                        self.logger.debug(f"发布平仓事件到Redis失败: {redis_e}")
                
                # 更新Prometheus平仓相关指标
                # 持仓状态通过结构化日志记录  # 设置为无持仓
                # 交易记录通过结构化日志记录
                
                # 更新总盈亏（这里应该从position_manager获取累计PnL）
                # TODO: 需要在position_manager中维护累计PnL
                # 盈亏统计通过结构化日志记录  # 临时使用单笔PnL作为总PnL
                
                self.logger.info(
                    f"🎉 套利平仓完成！两条腿都收到交易所成交确认 "
                    f"实现盈亏: {realized_pnl:.4f}"
                )
                
                # 空仓状态已由PositionManager自动保存
                
                # 清理策略状态
                self.strategy.clear_position_entry()
                
                # 记录关键统计
                total_stats = self.position_manager.get_statistics()
                self.logger.info(
                    f"📊 交易完成: 总交易{total_stats['total_trades']}次 "
                    f"胜率{total_stats['win_rate']:.2%} 总盈亏{total_stats['total_pnl']:.4f}"
                )
            else:
                self.logger.info(
                    f"🔄 单腿平仓完成，等待另一条腿: {inst_id}"
                )
                
        except Exception as e:
            self.logger.error(f"处理平仓订单实时成交失败: {e}")
    
    async def _update_position_order_info(self, order_info: Dict[str, Any]):
        """更新PositionManager中的订单信息"""
        try:
            if not self.position_manager.current_position:
                return
            
            # 从 position_manager 导入 OrderInfo
            from position_manager import OrderInfo
            
            inst_id = order_info.get("instId")
            order_id = order_info.get("ordId")
            side = order_info.get("side")
            filled_size = order_info.get("fillSz", "0")
            avg_price = order_info.get("avgPx")
            
            # 创建 OrderInfo 对象
            order_obj = OrderInfo(
                order_id=order_id,
                inst_id=inst_id,
                side=side,
                size=filled_size,
                price=avg_price,
                status="filled",
                fill_size=filled_size,
                avg_px=avg_price
            )
            
            # 更新对应的订单信息
            if inst_id == self.config.SPOT_ID:
                self.position_manager.current_position.spot_order = order_obj
                # 更新现货订单信息（Prometheus已记录）
                pass
            elif inst_id == self.config.FUTURES_ID:
                self.position_manager.current_position.futures_order = order_obj
                # 更新期货订单信息（Prometheus已记录）
                pass
                
        except Exception as e:
            self.logger.error(f"更新仓位订单信息失败: {e}")
    
    async def _check_both_legs_filled_for_entry(self) -> bool:
        """检查开仓的两条腿是否都成交（基于实时订单状态）"""
        try:
            # 检查是否有现货和期货的成交订单
            spot_filled = False
            futures_filled = False
            
            for order_id, order_data in self.open_orders.items():
                if order_data["state"] == "filled":
                    if order_data["inst_id"] == self.config.SPOT_ID:
                        spot_filled = True
                    elif order_data["inst_id"] == self.config.FUTURES_ID:
                        futures_filled = True
            
            return spot_filled and futures_filled
            
        except Exception as e:
            self.logger.error(f"检查开仓两条腿状态失败: {e}")
            return False
    
    async def _check_both_legs_filled_for_exit(self) -> bool:
        """检查平仓的两条腿是否都成交（基于实时订单状态）"""
        try:
            # 平仓逻辑与开仓类似，检查两条腿的平仓订单状态
            spot_filled = False
            futures_filled = False
            
            for order_id, order_data in self.open_orders.items():
                if order_data["state"] == "filled":
                    if order_data["inst_id"] == self.config.SPOT_ID:
                        spot_filled = True
                    elif order_data["inst_id"] == self.config.FUTURES_ID:
                        futures_filled = True
            
            return spot_filled and futures_filled
            
        except Exception as e:
            self.logger.error(f"检查平仓两条腿状态失败: {e}")
            return False
    
    async def _check_position_risks_realtime(self, position_data: Dict[str, Any]):
        """实时风险检查（取代轮询检查）"""
        try:
            # 检查时间止损
            if self.position_manager.check_time_stop_loss():
                self.logger.warning("⚠️  时间止损触发")
                await self._execute_exit("时间止损")
                return
            
            # 检查PnL止损
            if self.position_manager.check_pnl_stop_loss():
                self.logger.warning("⚠️  PnL止损触发")
                await self._execute_exit("PnL止损")
                return
            
            # 检查保证金风险
            if self.position_manager.check_margin_risk(position_data):
                self.logger.warning("⚠️  保证金风险触发")
                await self._execute_exit("保证金风险")
                return
                
        except Exception as e:
            self.logger.error(f"实时风险检查失败: {e}")
    
    # 保留原有的_process_price_update逻辑，但由handle_ticker_update直接调用_check_entry_signals
    async def _update_strategy_and_position(self):
        """更新策略和仓位状态"""
        try:
            # 更新策略价格
            strategy_ready = self.strategy.update_prices(
                self.spot_price, 
                self.futures_price, 
                high_price=self.spot_price,
                low_price=self.spot_price
            )
            
            if not strategy_ready:
                return False
            
            # 更新仓位的未实现盈亏（多交易对版本暂时跳过）
            # TODO: 需要根据持仓的具体交易对来更新PnL
            pass
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新策略和仓位状态失败: {e}")
            return False
    
    async def _check_all_strategies_async(self):
        """检查所有交易对策略 - 多交易对版本"""
        if self.is_opening_position:
            return  # 静默跳过，减少日志噪音
        
        try:
            # 检查所有交易对的入场信号
            signals = self.strategy_manager.check_all_entry_signals(self.funding_rates)
            
            # 调试日志
            if signals:
                self.logger.info(f"🔍 检测到{len(signals)}个信号，持仓状态: {self.position_manager.in_position}")
            
            if signals and self.position_manager.in_position:
                self.logger.info("⚠️  有活跃仓位，跳过新信号")
                return  # 简化：如果有持仓就不开新仓
            
            # 获取最优信号
            best_signal = self.strategy_manager.get_priority_signal(signals)
            
            if best_signal:
                self.logger.info(f"🎯 准备执行最优信号: {best_signal['pair_name']} - {best_signal['signal_type']}")
                await self._execute_multi_pair_signal(best_signal)
            elif signals:
                self.logger.warning(f"⚠️  有{len(signals)}个信号但get_priority_signal返回None")
            
            # 检查出场信号
            exit_pairs = self.strategy_manager.check_all_exit_signals()
            for pair_name in exit_pairs:
                await self._execute_pair_exit(pair_name)
                
        except Exception as e:
            self.logger.error(f"多交易对策略检查失败: {e}")
    
    async def _execute_multi_pair_signal(self, signal: Dict[str, Any]):
        """执行多交易对信号"""
        try:
            pair_name = signal["pair_name"]
            spot_symbol = signal["spot_symbol"] 
            futures_symbol = signal["futures_symbol"]
            signal_type = signal["signal_type"]
            position_size = signal["position_size"]
            
            self.logger.info(
                f"🎯 执行{pair_name}套利信号 - 类型:{signal_type}, "
                f"评级:{signal['risk_grade']}, 置信度:{signal['confidence']:.3f}"
            )
            
            # 记录信号指标
            # 信号统计通过结构化日志记录
            
            # 设置开仓锁定
            self.is_opening_position = True
            
            try:
                # 执行开仓交易
                success = await self._execute_arbitrage_trades(
                    pair_name, signal_type, position_size, 
                    spot_symbol, futures_symbol,
                    signal["current_spot_price"], signal["current_futures_price"]
                )
                
                if success:
                    # 更新策略管理器状态
                    self.strategy_manager.set_position_entry(
                        pair_name, signal_type, signal["current_spot_price"]
                    )
                
            finally:
                self.is_opening_position = False
                
        except Exception as e:
            self.logger.error(f"执行多交易对信号失败: {e}")
            self.is_opening_position = False
    
    async def _execute_pair_exit(self, pair_name: str):
        """执行交易对平仓"""
        try:
            pair_status = self.strategy_manager.get_pair_status(pair_name)
            if not pair_status or not pair_status["active_position"]:
                return
            
            self.logger.info(f"🚪 执行{pair_name}平仓 - 类型:{pair_status['position_type']}")
            
            # 执行平仓交易（具体实现需要根据position_manager接口调整）
            success = await self._close_position_for_pair(pair_name, pair_status)
            
            if success:
                # 清除策略管理器状态
                self.strategy_manager.clear_position_entry(pair_name)
                
        except Exception as e:
            self.logger.error(f"执行{pair_name}平仓失败: {e}")
    
    async def _execute_arbitrage_trades(self, pair_name: str, signal_type: str, 
                                       position_size: float, spot_symbol: str, 
                                       futures_symbol: str, spot_price: float, 
                                       futures_price: float) -> bool:
        """执行套利交易 - 多交易对版本"""
        try:
            self.logger.info(
                f"🚀 开始执行{pair_name}套利交易: {signal_type}, 仓位:{position_size}"
            )
            
            # 确定仓位类型 - 简化为单一套利类型
            if signal_type == "short_futures_long_spot":
                position_type = PositionType.SHORT_FUTURES_LONG_SPOT
            else:
                self.logger.error(f"未知信号类型: {signal_type}")
                return False
            
            # 创建仓位
            position_id = self.position_manager.create_position(
                position_type, spot_price, futures_price
            )
            
            # 确定交易方向
            spot_side, futures_side = self._determine_trade_sides(signal_type, "entry")
            
            # 使用动态合约规格计算期货数量
            futures_contracts = self.connector.calculate_futures_quantity(
                spot_quantity=position_size,
                spot_price=spot_price,
                futures_inst_id=futures_symbol
            )
            
            # 确保不少于最小交易单位
            min_contracts = self.connector.get_lot_size(futures_symbol)
            futures_contracts = max(futures_contracts, min_contracts)
            
            # 准备订单参数 - 移除ord_type让智能执行系统自动处理
            spot_params = {
                "inst_id": spot_symbol,
                "trade_mode": "cash",
                "side": spot_side,
                "sz": str(position_size)
            }
            
            futures_params = {
                "inst_id": futures_symbol,
                "trade_mode": "cross",
                "side": futures_side,
                "sz": str(futures_contracts)
            }
            
            self.logger.info(
                f"📋 订单参数: 现货({spot_side})={position_size}, 期货({futures_side})={futures_contracts:.6f}"
            )
            
            # 并发执行套利订单
            spot_result, futures_result = await self.connector.place_arbitrage_orders(
                spot_params, futures_params
            )
            
            # 检查执行结果
            spot_success = spot_result and spot_result.get("success", False)
            futures_success = futures_result and futures_result.get("success", False)
            
            if spot_success and futures_success:
                self.logger.info(f"✅ {pair_name}套利交易执行成功")
                return True
            else:
                self.logger.error(
                    f"❌ {pair_name}套利交易执行失败 - "
                    f"现货:{spot_success}, 期货:{futures_success}"
                )
                return False
                
        except Exception as e:
            self.logger.error(f"执行{pair_name}套利交易异常: {e}")
            return False
    
    async def _close_position_for_pair(self, pair_name: str, pair_status: Dict) -> bool:
        """平仓指定交易对 - 多交易对版本"""
        # 这里需要根据实际的position_manager接口来实现
        # 暂时返回True，具体实现需要在后续步骤中完成
        self.logger.info(f"平仓{pair_name}交易对")
        return True
    
    async def _check_entry_signals(self):
        """检查入场信号（事件驱动版本）"""
        # 并发开仓锁定检查
        if self.is_opening_position:
            return  # 静默跳过，减少日志噪音
            
        if self.position_manager.in_position:
            return  # 简化：如果有持仓就不开新仓
        
        try:
            # 确保策略状态已更新
            if not await self._update_strategy_and_position():
                return  # 策略未准备好
            
            # 计算基础仓位大小（用于净利润计算）
            base_position_size = self._calculate_position_size()
            
            # 使用净利润决策（整合所有交易成本和资金费率）
            signal = self.strategy.check_net_entry_signal(
                position_size=base_position_size,
                funding_rate=self.futures_funding_rate
            )
            
            if signal:
                # 记录Prometheus信号指标
                # 信号统计通过结构化日志记录
                
                # 获取微观结构仓位调整建议
                microstructure_multiplier = self.strategy.get_microstructure_position_multiplier(signal)
                final_position_size = base_position_size * microstructure_multiplier
                
                # 更新微观结构置信度指标
                microstructure_info = signal.get("microstructure_analysis", {})
                if microstructure_info.get('available', False):
                    micro_confidence = microstructure_info.get('micro_confidence', 0)
                    # 微观结构置信度通过结构化日志记录
                
                # 更新信号中的实际仓位大小
                signal['final_position_size'] = final_position_size
                signal['base_position_size'] = base_position_size
                signal['microstructure_multiplier'] = microstructure_multiplier
                
                net_analysis = signal.get("net_profit_analysis", {})
                microstructure_info = signal.get("microstructure_analysis", {})
                
                self.logger.info_structured(
                    f"净利润信号检测 - {signal['signal_type']}",
                    event_type="profitable_signal_detected",
                    metrics={
                        "signal_type": signal['signal_type'],
                        "risk_grade": signal['risk_grade'],
                        "confidence": signal['confidence'],
                        "original_confidence": signal.get('original_confidence'),
                        "potential_gross_profit": net_analysis.get('potential_gross_profit', 0),
                        "total_costs": net_analysis.get('total_costs', 0),
                        "net_profit": net_analysis.get('net_profit', 0),
                        "profit_margin": net_analysis.get('profit_margin', 0),
                        "base_position_size": base_position_size,
                        "final_position_size": final_position_size,
                        "microstructure_multiplier": microstructure_multiplier
                    },
                    details={
                        "funding_impact": net_analysis.get('funding_impact', 0),
                        "safety_margin": net_analysis.get('safety_margin_applied'),
                        "microstructure_available": microstructure_info.get('available', False),
                        "microstructure_signal": microstructure_info.get('micro_signal'),
                        "enhancement_reason": microstructure_info.get('enhancement_reason')
                    },
                    trace_id=signal.get('trace_id')
                )
                
                # 发布Redis信号事件（用于仪表盘）
                if self.redis_publisher:
                    try:
                        signal_data = {
                            "signal_type": signal['signal_type'],
                            "risk_grade": signal['risk_grade'],
                            "confidence": signal['confidence'],
                            "original_confidence": signal.get('original_confidence'),
                            "net_profit": net_analysis.get('net_profit', 0),
                            "profit_margin": net_analysis.get('profit_margin', 0),
                            "base_position_size": base_position_size,
                            "final_position_size": final_position_size,
                            "microstructure_multiplier": microstructure_multiplier,
                            "microstructure_available": microstructure_info.get('available', False),
                            "microstructure_signal": microstructure_info.get('micro_signal')
                        }
                        await self.redis_publisher.publish_signal_event(
                            self.config.TRADE_PAIR,
                            signal_data,
                            trace_id=signal.get('trace_id')
                        )
                    except Exception as redis_e:
                        self.logger.debug(f"发布信号事件到Redis失败: {redis_e}")
                
                # 额外的微观结构增强日志
                if microstructure_multiplier != 1.0:
                    enhancement_type = "增强" if microstructure_multiplier > 1.0 else "减弱"
                    self.logger.info(
                        f"🧠 微观结构{enhancement_type}: 仓位乘数 {microstructure_multiplier:.2f}x "
                        f"({base_position_size:.4f} → {final_position_size:.4f})"
                    )
                
                self.logger.info(f"📈 策略状态: {self.strategy.get_market_summary()}")
                await self._execute_entry(signal)
            else:
                # 定期记录策略状态（改为DEBUG级别）
                if self.price_log_counter % 100 == 0:
                    self.logger.debug(f"🔍 策略监控: {self.strategy.get_market_summary()}")
        
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"检查入场信号失败: {e}")
    
    async def _check_exit_signals(self):
        """检查出场信号（事件驱动版本）"""
        if not self.position_manager.in_position:
            return
        
        try:
            position_info = self.position_manager.get_position_info()
            position_type = position_info["position_type"]
            
            # 检查策略出场信号
            strategy_exit = self.strategy.check_exit_signal(position_type)
            
            if strategy_exit:
                self.logger.info("🚨 检测到策略出场信号")
                await self._execute_exit("策略信号")
                return
            
            # 风控检查由handle_position_update实时处理
        
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"检查出场信号失败: {e}")
    
    async def _execute_entry(self, signal: Dict[str, Any]):
        """执行入场交易"""
        try:
            # 提取trace_id并设置追踪上下文
            trace_id = signal.get("trace_id")
            if trace_id:
                trace_manager.set_trace_id(trace_id)
            
            # 设置开仓锁定标志
            self.is_opening_position = True
            
            signal_type = signal.get("signal_type")
            risk_grade = signal.get("risk_grade", "A")
            confidence = signal.get("confidence", 1.0)
            
            # 结构化日志记录交易开始
            self.logger.info_structured(
                "开始执行分级入场交易",
                event_type="trade_entry_start",
                details={
                    "signal_type": signal_type,
                    "risk_grade": risk_grade,
                    "confidence": confidence
                },
                trace_id=trace_id
            )
            
            # 确定仓位类型 - 简化为单一套利类型
            if signal_type == "short_futures_long_spot":
                position_type = PositionType.SHORT_FUTURES_LONG_SPOT
            else:
                self.logger.error(f"未知信号类型: {signal_type}")
                return
            
            # 创建仓位
            position_id = self.position_manager.create_position(
                position_type, self.spot_price, self.futures_price
            )
            
            # 使用通用方法执行交易
            trade_result = await self._execute_arbitrage_trade(
                position_type=signal_type,
                direction="entry",
                signal=signal
            )
            
            if trade_result["success"]:
                # 阶段三：实时差额对冲
                await self._stage_three_hedge_imbalance(
                    trade_result["spot_result"], 
                    trade_result["futures_result"], 
                    position_id, 
                    signal_type, 
                    signal
                )
            else:
                # 记录交易失败指标
                # 交易记录通过结构化日志记录
                # 错误统计通过结构化日志记录
                
                self.logger.error(f"交易执行失败: {trade_result.get('error', '未知错误')}")
                if self.position_manager.current_position:
                    self.position_manager.set_position_error(trade_result.get('error', '交易执行失败'))
            
        except Exception as e:
            self.logger.error(f"执行入场交易失败: {e}")
            if self.position_manager.current_position:
                self.position_manager.set_position_error(str(e))
        finally:
            # 无论成功、失败或异常，都要释放开仓锁定
            self.is_opening_position = False
            self.logger.info("🔓 释放开仓锁定")
    
    async def _stage_three_hedge_imbalance(self, spot_result: Dict, futures_result: Dict, 
                                          position_id: str, signal_type: str, signal: Dict[str, Any]):
        """阶段三：实时差额对冲逻辑"""
        try:
            self.logger.info("🔄 阶段三: 分析成交差额并执行对冲")
            
            # 检查执行结果格式
            spot_success = spot_result.get('success', False) if isinstance(spot_result, dict) else False
            futures_success = futures_result.get('success', False) if isinstance(futures_result, dict) else False
            
            if not spot_success and not futures_success:
                error_msg = "现货和期货订单均执行失败"
                self.logger.error(error_msg)
                self.position_manager.set_position_error(error_msg)
                return
            
            # 获取实际成交量
            spot_filled = spot_result.get('filled_size', 0.0) if spot_success else 0.0
            futures_filled = futures_result.get('filled_size', 0.0) if futures_success else 0.0
            
            # 转换期货成交量为等价现货数量（基于合约面值）
            futures_contract_value = self.connector.get_contract_value(self.config.FUTURES_ID)
            futures_equivalent = futures_filled * futures_contract_value
            
            # 计算差额（imbalance）
            imbalance = spot_filled - futures_equivalent
            hedge_threshold = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["MIN_HEDGE_THRESHOLD"]
            
            self.logger.info(
                f"📊 成交分析: 现货={spot_filled:.6f}, 期货={futures_filled:.6f} "
                f"(等价现货={futures_equivalent:.6f}), 差额={imbalance:.6f}"
            )
            
            # 检查是否需要对冲
            if abs(imbalance) < hedge_threshold:
                self.logger.info("✅ 成交差额在可接受范围内，无需对冲")
                await self._finalize_successful_entry(spot_result, futures_result, position_id, signal_type, signal)
                return
            
            # 执行对冲
            self.logger.warning(f"⚠️  检测到成交差额 {imbalance:.6f}，开始执行对冲")
            await self._execute_hedge_orders(imbalance, spot_filled, futures_filled)
            
            # 最终确认成功
            await self._finalize_successful_entry(spot_result, futures_result, position_id, signal_type, signal)
            
        except Exception as e:
            self.logger.error(f"差额对冲执行失败: {e}")
            self.position_manager.set_position_error(f"对冲失败: {str(e)}")
    
    async def _execute_hedge_orders(self, imbalance: float, spot_filled: float, futures_filled: float):
        """执行对冲订单"""
        try:
            max_attempts = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["MAX_HEDGE_ATTEMPTS"]
            
            for attempt in range(max_attempts):
                self.logger.info(f"🔄 执行对冲尝试 {attempt + 1}/{max_attempts}")
                
                if imbalance > 0:
                    # 现货多了，需要卖出现货或买入期货
                    # 优先买入期货（成本更低）
                    futures_needed = imbalance / self.connector.get_contract_value(self.config.FUTURES_ID)
                    
                    hedge_result = await self.connector.place_market_hedge_order(
                        inst_id=self.config.FUTURES_ID,
                        side="buy",
                        size=futures_needed
                    )
                    
                else:
                    # 期货多了，需要卖出期货或买入现货
                    # 优先卖出期货
                    futures_excess = abs(imbalance) / self.connector.get_contract_value(self.config.FUTURES_ID)
                    
                    hedge_result = await self.connector.place_market_hedge_order(
                        inst_id=self.config.FUTURES_ID,
                        side="sell",
                        size=futures_excess
                    )
                
                if hedge_result.get('code') == '0':
                    self.logger.info(f"✅ 对冲订单执行成功")
                    break
                else:
                    self.logger.warning(f"⚠️  对冲尝试 {attempt + 1} 失败: {hedge_result.get('msg')}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(0.1)  # 短暂等待后重试
            
        except Exception as e:
            self.logger.error(f"执行对冲订单异常: {e}")
            raise
    
    async def _finalize_successful_entry(self, spot_result: Dict, futures_result: Dict, 
                                        position_id: str, signal_type: str, signal: Dict[str, Any]):
        """完成成功入场的最终处理"""
        try:
            # 提取订单信息（适配智能执行结果格式）
            spot_order_ids = spot_result.get('order_ids', [])
            futures_order_ids = futures_result.get('order_ids', [])
            
            spot_order = OrderInfo(
                order_id=spot_order_ids[0] if spot_order_ids else "unknown",
                inst_id=self.config.SPOT_ID,
                side=spot_result.get('side', 'unknown'),
                size=str(spot_result.get('filled_size', 0))
            )
            
            futures_order = OrderInfo(
                order_id=futures_order_ids[0] if futures_order_ids else "unknown", 
                inst_id=self.config.FUTURES_ID,
                side=futures_result.get('side', 'unknown'),
                size=str(futures_result.get('filled_size', 0))
            )
            
            self.position_manager.update_position_orders(spot_order, futures_order)
            
            # 注意：confirm_position_open()已经在_handle_entry_order_filled_real_time()中
            # 基于真实订单成交事件调用，这里不再重复调用
            
            # 设置策略入场状态
            self.strategy.set_position_entry(signal_type)
            
            # 仓位状态已由PositionManager自动保存
            
            # 记录执行阶段统计
            self._log_execution_stages(spot_result, futures_result)
            
            self.logger.info(f"🎉 入场交易完全执行成功 - 仓位ID: {position_id}")
            
        except Exception as e:
            self.logger.error(f"完成入场处理失败: {e}")
            raise
    
    def _log_execution_stages(self, spot_result: Dict, futures_result: Dict):
        """记录执行阶段统计信息"""
        try:
            spot_stages = spot_result.get('execution_stages', [])
            futures_stages = futures_result.get('execution_stages', [])
            
            self.logger.debug("📈 执行阶段统计:")
            
            # 现货执行阶段
            self.logger.debug(f"   现货 ({self.config.SPOT_ID}):")
            for stage in spot_stages:
                stage_name = stage.get('stage', 'unknown')
                success = "✅" if stage.get('success') else "❌"
                self.logger.debug(f"     {stage_name}: {success}")
            
            # 期货执行阶段  
            self.logger.debug(f"   期货 ({self.config.FUTURES_ID}):")
            for stage in futures_stages:
                stage_name = stage.get('stage', 'unknown')
                success = "✅" if stage.get('success') else "❌"
                self.logger.debug(f"     {stage_name}: {success}")
                
        except Exception as e:
            # 执行阶段统计记录失败（非关键，已有Prometheus监控）
            pass
    
    async def _execute_arbitrage_trade(self, 
                                      position_type: str, 
                                      direction: str,
                                      signal: Dict[str, Any] = None,
                                      reason: str = None) -> Dict[str, Any]:
        """
        通用的套利交易执行方法
        
        Args:
            position_type: 仓位类型 ('short_futures_long_spot')
            direction: 交易方向 ('entry' | 'exit')
            signal: 入场信号字典（仅对entry有效）
            reason: 出场原因（仅对exit有效）
            
        Returns:
            Dict: 包含交易结果的字典
        """
        try:
            # 获取当前追踪ID（如果有的话）
            trace_id = trace_manager.get_trace_id()
            order_start_time = time.time()
            
            self.logger.info_structured(
                f"开始执行{direction}交易",
                event_type=f"trade_{direction}_execute",
                details={
                    "position_type": position_type,
                    "reason": reason
                },
                trace_id=trace_id
            )
            
            # 确定交易方向
            spot_side, futures_side = self._determine_trade_sides(position_type, direction)
            
            # 计算仓位大小
            if direction == "entry" and signal:
                position_size = self._calculate_graded_position_size(signal)
            else:
                position_size = self._calculate_position_size()
            
            futures_contracts = self._calculate_futures_contracts(position_size)
            
            # 准备订单参数
            spot_params = {
                "inst_id": self.config.SPOT_ID,
                "trade_mode": "cash",
                "side": spot_side,
                "ord_type": "market",
                "sz": str(position_size)
            }
            
            futures_params = {
                "inst_id": self.config.FUTURES_ID,
                "trade_mode": "cross",
                "side": futures_side,
                "ord_type": "market", 
                "sz": str(futures_contracts)
            }
            
            self.logger.debug(
                f"订单参数: 现货({spot_side})={position_size}, 期货({futures_side})={futures_contracts:.6f}"
            )
            
            # 并发执行三阶段智能套利订单
            action_name = "开仓" if direction == "entry" else "平仓"
            self.logger.info(f"🚀 执行三阶段智能{action_name}订单对")
            
            spot_result, futures_result = await self.connector.place_arbitrage_orders(
                spot_params, futures_params
            )
            
            # 计算订单延迟并记录KPI
            order_end_time = time.time()
            order_latency = KPICalculator.order_latency_ms(order_start_time, order_end_time)
            
            # 记录订单延迟指标
            # 订单延迟统计通过结构化日志记录
            
            # 记录订单执行结果
            spot_success = spot_result and spot_result.get("success", False)
            futures_success = futures_result and futures_result.get("success", False)
            
            if spot_success and futures_success:
                # 计算滑点
                spot_slippage = 0.0
                futures_slippage = 0.0
                
                if spot_result.get("avg_price") and spot_result.get("filled_size"):
                    # 市价单的滑点基于市场价格计算
                    spot_slippage = 0.0  # 市价单滑点难以准确计算
                
                if futures_result.get("avg_price") and futures_result.get("filled_size"):
                    futures_slippage = 0.0  # 市价单滑点难以准确计算
                
                self.logger.info_structured(
                    f"订单执行成功 - {action_name}",
                    event_type="orders_executed",
                    metrics={
                        "order_latency_ms": order_latency,
                        "spot_slippage_bps": spot_slippage,
                        "futures_slippage_bps": futures_slippage,
                        "spot_filled_size": spot_result.get("filled_size", 0),
                        "futures_filled_size": futures_result.get("filled_size", 0),
                        "spot_avg_price": spot_result.get("avg_price", 0),
                        "futures_avg_price": futures_result.get("avg_price", 0)
                    },
                    details={
                        "spot_side": spot_side,
                        "futures_side": futures_side,
                        "position_size": position_size,
                        "futures_contracts": futures_contracts
                    },
                    trace_id=trace_id
                )
            else:
                self.logger.error_structured(
                    f"订单执行失败 - {action_name}",
                    event_type="orders_failed",
                    metrics={
                        "order_latency_ms": order_latency
                    },
                    details={
                        "spot_success": spot_success,
                        "futures_success": futures_success,
                        "spot_error": spot_result.get("error") if spot_result else "No result",
                        "futures_error": futures_result.get("error") if futures_result else "No result"
                    },
                    trace_id=trace_id
                )
            
            return {
                "success": True,
                "spot_result": spot_result,
                "futures_result": futures_result,
                "position_size": position_size,
                "futures_contracts": futures_contracts,
                "spot_side": spot_side,
                "futures_side": futures_side,
                "order_latency_ms": order_latency
            }
            
        except Exception as e:
            self.logger.error(f"执行{direction}交易失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "spot_result": None,
                "futures_result": None
            }
    
    def _determine_trade_sides(self, position_type: str, direction: str) -> Tuple[str, str]:
        """
        根据仓位类型和交易方向确定现货和期货的买卖方向
        
        Args:
            position_type: 仓位类型 ('short_futures_long_spot')
            direction: 交易方向 ('entry' | 'exit')

        Returns:
            Tuple[str, str]: (现货方向, 期货方向)
        """
        if position_type == "short_futures_long_spot":
            if direction == "entry":
                return "buy", "sell"    # 基差套利：买现货，卖期货
            else:  # exit
                return "sell", "buy"    # 平仓套利：卖现货，买期货
        else:
            raise ValueError(f"未知的仓位类型: {position_type}")

    async def _execute_exit(self, reason: str):
        """执行出场交易"""
        try:
            self.logger.info(f"开始执行出场交易，原因: {reason}")
            
            if not self.position_manager.in_position:
                return
            
            position_info = self.position_manager.get_position_info()
            position_type = position_info["position_type"]
            
            # 开始平仓流程
            self.position_manager.start_position_close()
            
            # 使用通用方法执行交易
            trade_result = await self._execute_arbitrage_trade(
                position_type=position_type,
                direction="exit",
                reason=reason
            )
            
            if trade_result["success"]:
                # 阶段三：平仓差额对冲
                await self._stage_three_exit_hedge(
                    trade_result["spot_result"], 
                    trade_result["futures_result"], 
                    reason
                )
            else:
                # 记录平仓失败指标
                # 交易记录通过结构化日志记录
                # 错误统计通过结构化日志记录
                
                self.logger.error(f"平仓交易执行失败: {trade_result.get('error', '未知错误')}")
            
        except Exception as e:
            self.logger.error(f"执行出场交易失败: {e}")
    
    async def _stage_three_exit_hedge(self, spot_result: Dict, futures_result: Dict, reason: str):
        """平仓阶段三：差额对冲逻辑"""
        try:
            self.logger.info("🔄 平仓阶段三: 分析平仓差额并执行对冲")
            
            # 检查执行结果
            spot_success = spot_result.get('success', False) if isinstance(spot_result, dict) else False
            futures_success = futures_result.get('success', False) if isinstance(futures_result, dict) else False
            
            if not spot_success and not futures_success:
                error_msg = f"平仓失败 - 现货和期货订单均执行失败"
                self.logger.error(error_msg)
                self.position_manager.set_position_error(error_msg)
                return
            
            # 获取实际平仓量
            spot_filled = spot_result.get('filled_size', 0.0) if spot_success else 0.0
            futures_filled = futures_result.get('filled_size', 0.0) if futures_success else 0.0
            
            # 转换期货平仓量为等价现货数量
            futures_contract_value = self.connector.get_contract_value(self.config.FUTURES_ID)
            futures_equivalent = futures_filled * futures_contract_value
            
            # 计算平仓差额
            imbalance = spot_filled - futures_equivalent
            hedge_threshold = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["MIN_HEDGE_THRESHOLD"]
            
            self.logger.info(
                f"📊 平仓分析: 现货={spot_filled:.6f}, 期货={futures_filled:.6f} "
                f"(等价现货={futures_equivalent:.6f}), 差额={imbalance:.6f}"
            )
            
            # 检查是否需要对冲
            if abs(imbalance) >= hedge_threshold:
                self.logger.warning(f"⚠️  检测到平仓差额 {imbalance:.6f}，开始执行对冲")
                await self._execute_hedge_orders(imbalance, spot_filled, futures_filled)
            else:
                self.logger.info("✅ 平仓差额在可接受范围内，无需对冲")
            
            # 完成平仓处理
            await self._finalize_successful_exit(spot_result, futures_result, reason)
            
        except Exception as e:
            self.logger.error(f"平仓差额对冲执行失败: {e}")
            self.position_manager.set_position_error(f"平仓对冲失败: {str(e)}")
    
    async def _finalize_successful_exit(self, spot_result: Dict, futures_result: Dict, reason: str):
        """完成成功平仓的最终处理"""
        try:
            # 计算已实现盈亏（简化计算）
            realized_pnl = self.position_manager.current_position.unrealized_pnl
            
            # 确认平仓完成
            self.position_manager.confirm_position_close(realized_pnl)
            
            # 清除策略入场状态
            self.strategy.clear_position_entry()
            
            # 空仓状态已由PositionManager自动保存
            
            # 记录平仓执行阶段统计
            self._log_execution_stages(spot_result, futures_result)
            
            self.logger.info(f"🎉 出场交易完全执行成功 - 原因: {reason}, 盈亏: {realized_pnl:.4f}")
            
        except Exception as e:
            self.logger.error(f"完成平仓处理失败: {e}")
            raise
    
    async def _emergency_close_position(self, reason: str):
        """紧急平仓"""
        self.logger.critical(f"执行紧急平仓: {reason}")
        await self._execute_exit(f"紧急平仓: {reason}")
    
    
    def _calculate_futures_contracts(self, spot_position_size: float) -> float:
        """计算期货合约数量（基于现货仓位大小）"""
        try:
            futures_contract_value = self.connector.get_contract_value(self.config.FUTURES_ID)
            futures_lot_size = self.connector.get_lot_size(self.config.FUTURES_ID)
            futures_min_size = self.connector.get_min_size(self.config.FUTURES_ID)
            
            # 基于合约面值计算合约数量
            futures_contracts = spot_position_size / futures_contract_value
            
            # 确保满足最小要求
            futures_contracts = max(futures_contracts, futures_min_size)
            
            # 调整为最小交易单位的整数倍
            futures_contracts = (futures_contracts // futures_lot_size) * futures_lot_size
            
            # 最终验证
            if futures_contracts < futures_min_size:
                futures_contracts = futures_min_size
            
            return futures_contracts
            
        except Exception as e:
            self.logger.error(f"计算期货合约数量失败: {e}")
            return 1.0  # 默认最小值
    
    def _calculate_position_size(self) -> float:
        """计算仓位大小（使用动态规格信息）"""
        if not self.spot_price:
            return 100.0  # 默认值
        
        try:
            # 获取现货最小交易单位
            spot_lot_size = self.connector.get_lot_size(self.config.SPOT_ID)
            spot_min_size = self.connector.get_min_size(self.config.SPOT_ID)
            
            # 计算基础仓位大小
            from config import calculate_position_size
            base_position_size = calculate_position_size(
                self.spot_price, 
                risk_ratio=0.5
            )
            
            # 确保仓位大小符合交易规格
            min_required = max(spot_lot_size, spot_min_size)
            position_size = max(base_position_size, min_required)
            
            # 调整为最小交易单位的整数倍
            position_size = (position_size // spot_lot_size) * spot_lot_size
            
            # 验证最终结果
            if position_size < spot_min_size:
                position_size = spot_min_size
            
            # 仓位大小计算详情（现已由Prometheus指标监控）
            pass
            
            return position_size
            
        except Exception as e:
            self.logger.error(f"计算仓位大小失败: {e}")
            return 100.0
    
    def _calculate_graded_position_size(self, signal: Dict[str, Any]) -> float:
        """计算分级风险仓位大小（含微观结构增强）"""
        if not self.spot_price:
            return 100.0  # 默认值
        
        try:
            # 优先使用已经计算好的微观结构增强仓位大小
            final_position_size = signal.get('final_position_size')
            if final_position_size is not None:
                # 微观结构增强的仓位大小（现已由Prometheus微观结构置信度指标监控）
                pass
                return final_position_size
            
            # 回退到传统分级风险计算（如果没有微观结构数据）
            base_position_size = self._calculate_position_size()
            
            # 获取风险评级和置信度
            risk_grade = signal.get("risk_grade", "A")
            confidence = signal.get("confidence", 1.0)
            
            # 获取分级配置
            graded_config = self.config.RISK_MANAGEMENT.get("GRADED_POSITION_SYSTEM", {})
            suboptimal_ratio = graded_config.get("SUBOPTIMAL_TRADE_SIZE_RATIO", 0.5)
            min_position_ratio = graded_config.get("MIN_POSITION_RATIO", 0.2)
            
            # 根据风险评级调整仓位
            if risk_grade == "A":
                # A级信号：使用满仓位，但根据置信度微调
                size_multiplier = confidence
            elif risk_grade == "B":
                # B级信号：使用次优仓位比例
                size_multiplier = suboptimal_ratio * confidence
            else:
                # 未知评级：使用最小仓位
                size_multiplier = min_position_ratio
            
            # 确保不低于最小仓位比例
            size_multiplier = max(size_multiplier, min_position_ratio)
            
            # 计算最终仓位大小
            adjusted_position_size = base_position_size * size_multiplier
            
            # 确保符合交易规格
            spot_lot_size = self.connector.get_lot_size(self.config.SPOT_ID)
            spot_min_size = self.connector.get_min_size(self.config.SPOT_ID)
            
            # 调整为最小交易单位的整数倍
            adjusted_position_size = (adjusted_position_size // spot_lot_size) * spot_lot_size
            
            # 验证最终结果
            if adjusted_position_size < spot_min_size:
                adjusted_position_size = spot_min_size
            
            # 分级仓位计算详情（现已由Prometheus指标监控仓位大小变化）
            pass
            
            return adjusted_position_size
            
        except Exception as e:
            self.logger.error(f"计算分级仓位大小失败: {e}")
            return self._calculate_position_size()  # 回退到基础计算
    
    # ======= 原有轮询任务已移除，由事件驱动替代 =======
    
    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            # 检查连接器健康状态
            connector_healthy = await self.connector.health_check()
            
            # 检查数据流新鲜度
            data_fresh = True
            if self.last_price_update_time:
                time_since_update = datetime.now() - self.last_price_update_time
                timeout = timedelta(seconds=self.config.MONITORING["STALE_DATA_TIMEOUT_SECONDS"])
                data_fresh = time_since_update < timeout
            
            # 检查紧急停止状态
            if self.emergency_stop:
                self.logger.critical("检测到紧急停止状态，准备关闭系统")
                await self.stop()
                return
            
            # 记录健康状态
            if not connector_healthy:
                self.logger.warning("连接器健康检查失败")
            
            if not data_fresh:
                self.logger.warning("价格数据流已过时")

            # 定期报告市场健康度状态
            market_health = self.get_market_health_summary()
            if market_health.get("market_health_score", 100) < 80:
                self.logger.warning_structured(
                    f"市场健康度评分: {market_health.get('market_health_score', 100):.1f} - {market_health.get('health_status', '未知')}",
                    event_type="market_health_report",
                    metrics=market_health.get("recent_anomalies_1h", {}),
                    details=market_health
                )

                # 发布市场健康度警报到Redis
                if self.redis_publisher:
                    try:
                        await self.redis_publisher.publish_alert(
                            "WARNING",
                            f"市场健康度评分低: {market_health.get('market_health_score', 100):.1f}",
                            {"market_health": market_health}
                        )
                    except Exception as redis_e:
                        self.logger.debug(f"发布市场健康度警报到Redis失败: {redis_e}")

            # 发布系统状态更新到Redis
            if self.redis_publisher:
                try:
                    runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
                    system_details = {
                        "start_time": self.start_time.isoformat() if self.start_time else None,
                        "runtime_hours": runtime.total_seconds() / 3600,
                        "trade_pair": getattr(self.config, 'TRADE_PAIR', 'Unknown'),
                        "subscribed_instruments": len(getattr(self, 'subscribed_spot_instruments', []) + getattr(self, 'subscribed_futures_instruments', [])),
                        "position_active": self.position_manager.in_position if hasattr(self, 'position_manager') else False,
                        "connector_healthy": connector_healthy,
                        "data_fresh": data_fresh,
                        "market_health_score": market_health.get("market_health_score", 100),
                        "emergency_stop": self.emergency_stop
                    }
                    await self.redis_publisher.publish_system_status("Running", system_details)
                except Exception as redis_e:
                    self.logger.debug(f"发布系统状态到Redis失败: {redis_e}")

            # 发布连接器健康状态警报
            if not connector_healthy and self.redis_publisher:
                try:
                    await self.redis_publisher.publish_alert(
                        "ERROR",
                        "连接器健康检查失败",
                        {"component": "okx_connector", "healthy": connector_healthy}
                    )
                except Exception as redis_e:
                    self.logger.debug(f"发布连接器警报到Redis失败: {redis_e}")

            # 发布数据新鲜度警报
            if not data_fresh and self.redis_publisher:
                try:
                    time_since_update = datetime.now() - self.last_price_update_time if self.last_price_update_time else timedelta(0)
                    await self.redis_publisher.publish_alert(
                        "WARNING",
                        f"价格数据流已过时 ({time_since_update.total_seconds():.0f}秒)",
                        {"component": "price_data", "last_update": self.last_price_update_time.isoformat() if self.last_price_update_time else None}
                    )
                except Exception as redis_e:
                    self.logger.debug(f"发布数据新鲜度警报到Redis失败: {redis_e}")

        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
    
    # 统计报告由定时检查处理，不再使用独立轮询任务
    
    def _log_statistics(self):
        """记录统计信息"""
        try:
            # 性能统计
            avg_processing_time = 0
            if self.processing_times:
                avg_processing_time = sum(self.processing_times) / len(self.processing_times)
            
            # 获取策略和仓位统计
            strategy_state = self.strategy.get_current_state()
            position_stats = self.position_manager.get_statistics()
            
            # 运行时长
            runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
            
            self.logger.info(
                f"=== 机器人状态报告 ===\n"
                f"运行时长: {runtime}\n"
                f"消息处理数: {self.message_count}\n"
                f"平均处理延迟: {avg_processing_time:.2f}ms\n"
                f"错误计数: {self.error_count}\n"
                f"当前价格: 现货={self.spot_price}, 期货={self.futures_price}\n"
                f"策略状态: {self.strategy.get_market_summary()}\n"
                f"仓位统计: {position_stats}\n"
                f"===================="
            )
        
        except Exception as e:
            self.logger.error(f"记录统计信息失败: {e}")
    
    def _log_final_statistics(self):
        """记录最终统计信息"""
        try:
            position_stats = self.position_manager.get_statistics()
            runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
            
            self.logger.info(
                f"=== 最终统计报告 ===\n"
                f"总运行时长: {runtime}\n"
                f"总处理消息数: {self.message_count}\n"
                f"总交易次数: {position_stats['total_trades']}\n"
                f"胜率: {position_stats['win_rate']:.2%}\n"
                f"总盈亏: {position_stats['total_pnl']:.4f}\n"
                f"平均每笔盈亏: {position_stats['avg_pnl_per_trade']:.4f}\n"
                f"总错误数: {self.error_count}\n"
                f"==================="
            )
        
        except Exception as e:
            self.logger.error(f"记录最终统计失败: {e}")
    
    def get_realtime_state(self) -> Dict[str, Any]:
        """获取实时状态概览"""
        return {
            "prices": {
                "spot_price": self.spot_price,
                "futures_price": self.futures_price,
                "last_update": self.last_price_update_time.isoformat() if self.last_price_update_time else None,
                "basis": (self.futures_price - self.spot_price) / self.spot_price if self.spot_price and self.futures_price else None
            },
            "funding": {
                "rate": self.futures_funding_rate,
                "last_update": self.last_funding_update_time.isoformat() if self.last_funding_update_time else None
            },
            "positions": {
                "count": len(self.current_positions),
                "last_update": self.last_position_update_time.isoformat() if self.last_position_update_time else None,
                "position_manager_status": "in_position" if self.position_manager.in_position else "no_position"
            },
            "orders": {
                "open_count": len(self.open_orders),
                "last_update": self.last_order_update_time.isoformat() if self.last_order_update_time else None
            },
            "account": {
                "balances_count": len(self.account_balance),
                "last_update": self.last_balance_update_time.isoformat() if self.last_balance_update_time else None
            },
            "performance": {
                "message_count": self.message_count,
                "error_count": self.error_count,
                "avg_processing_time_ms": sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0,
                "is_opening_position": self.is_opening_position
            },
            "running_status": {
                "is_running": self.is_running,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "emergency_stop": self.emergency_stop
            }
        }
    
    def __repr__(self):
        status = "事件驱动运行中" if self.is_running else "已停止"
        position_status = "有仓位" if self.position_manager.in_position else "无仓位"
        return f"ArbitrageBot(status={status}, {position_status}, messages={self.message_count})"